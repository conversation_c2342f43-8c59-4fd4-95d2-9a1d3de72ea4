'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'

interface HighlightedText {
  id: string
  text: string
  startLine: number
  endLine: number
  startColumn: number
  endColumn: number
  filePath: string
}

interface TsxFilePreviewProps {
  content: string
  filePath: string
  onTextSelect: (selectedText: string, startLine: number, endLine: number, startColumn: number, endColumn: number) => void
  highlightedText: HighlightedText | null
}

export default function TsxFilePreview({
  content,
  filePath,
  onTextSelect,
  highlightedText
}: TsxFilePreviewProps) {
  const [selectedRange, setSelectedRange] = useState<{
    startLine: number
    endLine: number
    startColumn: number
    endColumn: number
    text: string
  } | null>(null)
  const previewRef = useRef<HTMLDivElement>(null)

  const lines = content.split('\n')

  const handleTextSelection = () => {
    const selection = window.getSelection()
    if (!selection || selection.isCollapsed || !previewRef.current) return

    const range = selection.getRangeAt(0)
    const selectedText = selection.toString().trim()
    
    if (!selectedText) return

    // Find the line numbers and column positions
    const preElement = previewRef.current.querySelector('pre')
    if (!preElement) return

    const textContent = preElement.textContent || ''
    const startOffset = textContent.indexOf(selectedText)
    const endOffset = startOffset + selectedText.length

    if (startOffset === -1) return

    // Calculate line and column positions
    const beforeStart = textContent.substring(0, startOffset)
    const beforeEnd = textContent.substring(0, endOffset)
    
    const startLine = beforeStart.split('\n').length
    const endLine = beforeEnd.split('\n').length
    
    const startLineContent = beforeStart.split('\n').pop() || ''
    const endLineContent = beforeEnd.split('\n').pop() || ''
    
    const startColumn = startLineContent.length + 1
    const endColumn = endLineContent.length + 1

    setSelectedRange({
      startLine,
      endLine,
      startColumn,
      endColumn,
      text: selectedText
    })

    onTextSelect(selectedText, startLine, endLine, startColumn, endColumn)
  }

  const renderLineWithHighlight = (line: string, lineNumber: number) => {
    if (!highlightedText) return line

    const isHighlightedLine = lineNumber >= highlightedText.startLine && lineNumber <= highlightedText.endLine
    
    if (!isHighlightedLine) return line

    // If it's a single line highlight
    if (highlightedText.startLine === highlightedText.endLine) {
      const before = line.substring(0, highlightedText.startColumn - 1)
      const highlighted = line.substring(highlightedText.startColumn - 1, highlightedText.endColumn - 1)
      const after = line.substring(highlightedText.endColumn - 1)
      
      return (
        <>
          {before}
          <span className="bg-yellow-200 px-1 rounded">{highlighted}</span>
          {after}
        </>
      )
    }
    
    // Multi-line highlight
    if (lineNumber === highlightedText.startLine) {
      const before = line.substring(0, highlightedText.startColumn - 1)
      const highlighted = line.substring(highlightedText.startColumn - 1)
      
      return (
        <>
          {before}
          <span className="bg-yellow-200 px-1 rounded">{highlighted}</span>
        </>
      )
    }
    
    if (lineNumber === highlightedText.endLine) {
      const highlighted = line.substring(0, highlightedText.endColumn - 1)
      const after = line.substring(highlightedText.endColumn - 1)
      
      return (
        <>
          <span className="bg-yellow-200 px-1 rounded">{highlighted}</span>
          {after}
        </>
      )
    }
    
    // Middle lines of multi-line highlight
    return <span className="bg-yellow-200 px-1 rounded">{line}</span>
  }

  return (
    <div ref={previewRef} className="h-full flex flex-col">
      <div className="flex-1 overflow-auto bg-gray-50">
        <pre className="text-sm font-mono p-4 leading-relaxed select-text">
          <code>
            {lines.map((line, index) => (
              <div
                key={index}
                className="flex hover:bg-gray-100 transition-colors"
                onMouseUp={handleTextSelection}
              >
                <span className="text-gray-400 select-none w-12 text-right pr-4 flex-shrink-0">
                  {index + 1}
                </span>
                <span className="flex-1 whitespace-pre-wrap">
                  {renderLineWithHighlight(line, index + 1)}
                </span>
              </div>
            ))}
          </code>
        </pre>
      </div>
      
      {selectedRange && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-blue-50 border-t border-blue-200"
        >
          <div className="text-sm text-blue-800">
            <strong>Selected:</strong> Lines {selectedRange.startLine}-{selectedRange.endLine}, 
            Columns {selectedRange.startColumn}-{selectedRange.endColumn}
          </div>
          <div className="text-xs text-blue-600 mt-1 font-mono bg-white p-2 rounded border max-h-20 overflow-y-auto">
            {selectedRange.text}
          </div>
        </motion.div>
      )}
    </div>
  )
}
