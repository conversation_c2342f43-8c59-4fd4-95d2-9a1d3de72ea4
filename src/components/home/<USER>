'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import { useStaticContent } from '@/lib/hooks/use-static-content'

interface HeroSlide {
  id: string
  title: string
  subtitle: string
  buttonText: string
  buttonUrl: string
  imageUrl: string
  displayOrder: number
  isActive: boolean
}

interface HeroCarouselProps {
  slides: HeroSlide[]
  autoPlay?: boolean
  autoPlayInterval?: number
  showNavigation?: boolean
  showIndicators?: boolean
  className?: string
}

export default function HeroCarousel({
  slides,
  autoPlay = true,
  autoPlayInterval = 5000,
  showNavigation = true,
  showIndicators = true,
  className = ''
}: HeroCarouselProps) {
  const { getContent } = useStaticContent();
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay)

  // Filter active slides and sort by display order
  const activeSlides = slides
    .filter(slide => slide.isActive)
    .sort((a, b) => a.displayOrder - b.displayOrder)

  useEffect(() => {
    if (!isAutoPlaying || activeSlides.length <= 1) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % activeSlides.length)
    }, autoPlayInterval)

    return () => clearInterval(interval)
  }, [isAutoPlaying, autoPlayInterval, activeSlides.length])

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
    setIsAutoPlaying(false)
    // Resume auto-play after 10 seconds of inactivity
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const goToPrevious = () => {
    setCurrentSlide((prev) => (prev - 1 + activeSlides.length) % activeSlides.length)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const goToNext = () => {
    setCurrentSlide((prev) => (prev + 1) % activeSlides.length)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  if (!activeSlides.length) {
    return (
      <div className={`relative h-96 bg-gray-100 flex items-center justify-center ${className}`}>
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-600 mb-2">{getContent('home', 'hero', 'no_content_title', 'No Hero Contenttttt')}</h2>
          <p className="text-gray-500">{getContent('home', 'hero', 'no_content_message', 'Please add hero slides in the admin panel')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Slides */}
      <div className="relative h-96 md:h-[500px] lg:h-[600px]">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="absolute inset-0"
          >
            <div className="relative w-full h-full">
              {/* Background Image */}
              <img
                src={activeSlides[currentSlide].imageUrl}
                alt={activeSlides[currentSlide].title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = '/images/placeholder.jpg'
                }}
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-transparent"></div>
              
              {/* Content */}
              <div className="absolute inset-0 flex items-center">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                    className="text-white"
                  >
                    <h1 
                      className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight"
                      data-content-section="hero"
                      data-content-key="title"
                      data-edit-id="hero-title"
                    >
                      {activeSlides[currentSlide].title}
                    </h1>
                    <p 
                      className="text-xl md:text-2xl mb-8 text-gray-200 max-w-2xl"
                      data-content-section="hero"
                      data-content-key="subtitle"
                      data-edit-id="hero-subtitle"
                    >
                      {activeSlides[currentSlide].subtitle}
                    </p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-lg"
                      data-slide-field="buttonText"
                      onClick={() => {
                        if (activeSlides[currentSlide].buttonUrl.startsWith('http')) {
                          window.open(activeSlides[currentSlide].buttonUrl, '_blank')
                        } else {
                          window.location.href = activeSlides[currentSlide].buttonUrl
                        }
                      }}
                    >
                      {activeSlides[currentSlide].buttonText}
                    </motion.button>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Navigation Arrows */}
      {showNavigation && activeSlides.length > 1 && (
        <>
          <button
            onClick={goToPrevious}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full transition-colors duration-200 backdrop-blur-sm"
            aria-label="Previous slide"
          >
            <ChevronLeftIcon className="h-6 w-6" />
          </button>
          <button
            onClick={goToNext}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full transition-colors duration-200 backdrop-blur-sm"
            aria-label="Next slide"
          >
            <ChevronRightIcon className="h-6 w-6" />
          </button>
        </>
      )}

      {/* Indicators */}
      {showIndicators && activeSlides.length > 1 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {activeSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentSlide
                  ? 'bg-white scale-110'
                  : 'bg-white/50 hover:bg-white/75'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Slide Counter */}
      {activeSlides.length > 1 && (
        <div className="absolute top-4 right-4 bg-black/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
          {currentSlide + 1} / {activeSlides.length}
        </div>
      )}
    </div>
  )
}

// Hook for managing hero slides from API
export function useHeroSlides(pageId: string, sectionId: string) {
  const [slides, setSlides] = useState<HeroSlide[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchSlides = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/content/pages')
        
        if (!response.ok) {
          throw new Error('Failed to fetch hero slides')
        }

        const data = await response.json()
        const page = data.pages?.find((p: any) => p.id === pageId)
        const section = page?.sections?.find((s: any) => s.id === sectionId)
        
        if (section?.slides) {
          setSlides(section.slides)
        } else {
          setSlides([])
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
        setSlides([])
      } finally {
        setLoading(false)
      }
    }

    fetchSlides()
  }, [pageId, sectionId])

  return { slides, loading, error }
} 