'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useStaticContent } from '@/lib/hooks/use-static-content';

interface ClientLogo {
  name: string;
  logo: string;
  url: string;
}

interface ClientLogosSectionProps {
  logos: ClientLogo[];
}

export function ClientLogosSection({ logos }: ClientLogosSectionProps) {
  const { getContent } = useStaticContent();

  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 to-blue-50/30">
      <div className="container px-6 mx-auto">
        <div className="section-header text-center mb-12">
          <p className="text-sm font-semibold text-gray-600 mb-8">
            {getContent('home', 'clients', 'subtitle', 'Trusted by industry leaders worldwide')}
          </p>
        </div>

        <div className="overflow-hidden">
          <div className="client-logos-scroll flex space-x-12">
            {/* Duplicate logos for seamless infinite scroll */}
            {[...logos, ...logos].map((client, index) => (
              <Link
                key={`${client.name}-${index}`}
                href={client.url}
                className="flex-shrink-0 group"
              >
                <div className="w-32 h-16 bg-white rounded-lg border border-gray-200 flex items-center justify-center hover:shadow-lg hover:border-[#d0ebff] transition-all duration-300 hover:-translate-y-1 p-4">
                  <Image
                    src={client.logo}
                    alt={client.name}
                    width={120}
                    height={60}
                    className="max-w-full max-h-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
                    loading="lazy"
                  />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
