'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import {
  XMarkIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import EditModal from '@/components/admin/visual-editor/edit-modal'
import ContentAwareIframe from '@/components/admin/visual-editor/content-aware-iframe'
import TsxFilePreview from '@/components/admin/visual-editor/tsx-file-preview'

interface PageData {
  id: string
  name: string
  path: string
  description: string
  lastModified: string
  status: 'draft' | 'published'
}

interface EditableElement {
  id: string
  text: string
  description: string
  filePath: string
  line?: number
  rect: DOMRect
  className?: string
  elementPath?: string
  selector?: string
  tagName?: string
  sourceLocation?: string
  lineNumber?: number
  columnStart?: number
  elementInfo?: {
    type: 'getContent' | 'stats' | 'const' | 'general' | 'database'
    page?: string
    section?: string
    key?: string
    varName?: string
  }
}

interface ChangeHistory {
  id: string
  type: 'text' | 'style' | 'layout'
  description: string
  timestamp: Date
  elementId: string
  oldValue: string
  newValue: string
}

interface TsxFileData {
  filePath: string
  content: string
  lastModified: string
}

interface HighlightedText {
  id: string
  text: string
  startLine: number
  endLine: number
  startColumn: number
  endColumn: number
  filePath: string
}

function VisualEditor() {
  const { data: session, status } = useSession()
  const [previewUrl, setPreviewUrl] = useState('')
  const [interactiveMode, setInteractiveMode] = useState(false)
  const [selectedElement, setSelectedElement] = useState<EditableElement | null>(null)
  const [editingValue, setEditingValue] = useState('')
  const [editFormPosition, setEditFormPosition] = useState({ x: 0, y: 0 })
  const [isClient, setIsClient] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [pages, setPages] = useState<any[]>([])
  const [selectedPage, setSelectedPage] = useState('')

  // TSX File Editor States
  const [showTsxEditor, setShowTsxEditor] = useState(false)
  const [selectedTsxFile, setSelectedTsxFile] = useState<TsxFileData | null>(null)
  const [tsxFiles, setTsxFiles] = useState<TsxFileData[]>([])
  const [highlightedText, setHighlightedText] = useState<HighlightedText | null>(null)
  const [tsxEditingValue, setTsxEditingValue] = useState('')

  // iframeRef is no longer needed as we use ContentAwareIframe

  // Set client-side flag to prevent hydration issues
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (status === 'loading') return
    if (!session?.user || session.user.role !== 'ADMIN') {
      redirect('/auth/signin')
    }
  }, [session, status])

  // Load pages
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.role === 'ADMIN') {
      loadPages()
    }
  }, [session, status])

  // Load TSX files and initialize preview URL
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.role === 'ADMIN') {
      loadTsxFiles()
      if (typeof window !== 'undefined') {
        const baseUrl = `${window.location.protocol}//${window.location.host}`
        setPreviewUrl(`${baseUrl}/`)
        setSelectedPage('home') // Default to home page
      }
    }
  }, [session, status])

  // Content-aware iframe handles its own script injection
  useEffect(() => {
    console.log('Interactive mode changed:', interactiveMode);
  }, [interactiveMode])

  const loadPages = async () => {
    try {
      const response = await fetch('/api/content/pages', {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.pages) {
          setPages(data.pages)
        }
      }
    } catch (error) {
      console.error('Error loading pages:', error)
    }
  }

  const loadTsxFiles = async () => {
    try {
      const response = await fetch('/api/content/tsx-files', {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.files) {
          setTsxFiles(data.files)
        }
      }
    } catch (error) {
      console.error('Error loading TSX files:', error)
    }
  }

  const enableInteractiveMode = () => {
    console.log('Enabling interactive mode')
    setInteractiveMode(true)
  }

  const disableInteractiveMode = () => {
    console.log('Disabling interactive mode')
    setInteractiveMode(false)
  }

  const handleElementSelection = async (element: EditableElement) => {
    setSelectedElement(element)
    setEditingValue(element.text)
    setEditFormPosition({
      x: Math.min(element.rect.left + element.rect.width / 2, window.innerWidth - 200),
      y: Math.max(element.rect.top - 10, 10)
    })

    // Find the source location of this text
    try {
      const response = await fetch(`/api/content/source-update?text=${encodeURIComponent(element.text)}&pagePath=${encodeURIComponent(element.filePath)}`, {
        credentials: 'include'
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Text locations found:', result.data)

        if (result.data.locations && result.data.locations.length > 0) {
          // If multiple locations found, use the first one but show info about all
          const firstLocation = result.data.locations[0]
          const locationInfo = result.data.totalOccurrences > 1
            ? `${firstLocation.locationDescription} (${result.data.totalOccurrences} occurrences found)`
            : firstLocation.locationDescription

          // Update the element with location information
          setSelectedElement({
            ...element,
            sourceLocation: locationInfo,
            filePath: firstLocation.filePath,
            lineNumber: firstLocation.lineNumber,
            columnStart: firstLocation.columnStart
          })

          // If multiple occurrences, show a warning
          if (result.data.totalOccurrences > 1) {
            console.log(`Found ${result.data.totalOccurrences} occurrences of "${element.text}":`)
            result.data.locations.forEach((loc: any, index: number) => {
              console.log(`${index + 1}. ${loc.locationDescription}`)
            })
          }
        } else {
          console.log('Could not find text location in source files')
        }
      } else {
        console.log('Could not find text location in source files')
      }
    } catch (error) {
      console.error('Error finding text location:', error)
    }
  }

  const saveElementEdit = async () => {
    if (!selectedElement || !editingValue.trim()) return

    setIsSaving(true)

    try {
      const requestBody = {
        oldText: selectedElement.text,
        newText: editingValue,
        pagePath: selectedElement.filePath,
        targetLine: Number(selectedElement.lineNumber),
        targetFile: selectedElement.sourceLocation?.split(' (')[0]
      }

      console.log('Sending source update request:', requestBody)

      const response = await fetch('/api/content/source-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody)
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Content updated successfully:', result)

        // Send message to iframe to update the specific element
        if (selectedElement) {
          const iframe = document.querySelector('iframe');
          if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage({
              type: 'UPDATE_ELEMENT_TEXT',
              elementId: selectedElement.id,
              newText: editingValue
            }, '*');
          }
        }

        setSelectedElement(null)
        setEditingValue('')
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('API Error:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error saving element edit:', error)
      alert(`Failed to save changes: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSaving(false)
    }
  }

  const handleTsxFileSelect = async (filePath: string) => {
    try {
      const response = await fetch(`/api/content/tsx-file-content?filePath=${encodeURIComponent(filePath)}`, {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setSelectedTsxFile({
            filePath,
            content: data.content,
            lastModified: data.lastModified
          })
        }
      }
    } catch (error) {
      console.error('Error loading TSX file content:', error)
    }
  }

  const handleTextHighlight = (selectedText: string, startLine: number, endLine: number, startColumn: number, endColumn: number) => {
    if (!selectedTsxFile) return

    const highlighted: HighlightedText = {
      id: Date.now().toString(),
      text: selectedText,
      startLine,
      endLine,
      startColumn,
      endColumn,
      filePath: selectedTsxFile.filePath
    }

    setHighlightedText(highlighted)
    setTsxEditingValue(selectedText)
  }

  const openTsxEditor = async () => {
    if (!selectedElement) return

    // Load TSX files if not already loaded
    if (tsxFiles.length === 0) {
      await loadTsxFiles()
    }

    // Find the source file for the selected element
    const sourceFile = selectedElement.sourceLocation?.split(' (')[0] || selectedElement.filePath

    if (sourceFile) {
      // Load the file content
      await handleTsxFileSelect(sourceFile)

      // Create highlighted text from selected element
      const highlighted: HighlightedText = {
        id: selectedElement.id,
        text: selectedElement.text,
        startLine: selectedElement.lineNumber || 1,
        endLine: selectedElement.lineNumber || 1,
        startColumn: selectedElement.columnStart || 1,
        endColumn: (selectedElement.columnStart || 1) + selectedElement.text.length,
        filePath: sourceFile
      }

      setHighlightedText(highlighted)
      setTsxEditingValue(selectedElement.text)
    }

    setShowTsxEditor(true)
  }

  const saveTsxEdit = async () => {
    if (!highlightedText || !tsxEditingValue.trim()) return

    setIsSaving(true)

    try {
      const response = await fetch('/api/content/tsx-edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          filePath: highlightedText.filePath,
          oldText: highlightedText.text,
          newText: tsxEditingValue,
          startLine: highlightedText.startLine,
          endLine: highlightedText.endLine,
          startColumn: highlightedText.startColumn,
          endColumn: highlightedText.endColumn
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('TSX file updated successfully:', result)

        // Reload the file content
        await handleTsxFileSelect(highlightedText.filePath)

        // Update the original element's text
        if (selectedElement) {
          setEditingValue(tsxEditingValue)
        }

        // Clear TSX editor selection
        setHighlightedText(null)
        setTsxEditingValue('')
        setShowTsxEditor(false)

      } else {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error saving TSX edit:', error)
      alert(`Failed to save changes: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSaving(false)
    }
  }

  // injectInteractiveScript function removed - ContentAwareIframe handles script injection

  const handleIframeMessage = useCallback((event: MessageEvent) => {
    console.log('Received message from iframe:', event.data);
    if (event.data.type === 'ELEMENT_SELECTED') {
      console.log('Element selected:', event.data.element);
      handleElementSelection(event.data.element)
    }
  }, [])

  useEffect(() => {
    window.addEventListener('message', handleIframeMessage)
    return () => window.removeEventListener('message', handleIframeMessage)
  }, [handleIframeMessage])

  const undo = () => {
    if (undoStack.length === 0) return
    
    const lastChange = undoStack[0]
    setUndoStack(prev => prev.slice(1))
    setRedoStack(prev => [lastChange, ...prev])
    
    // TODO: Implement undo functionality with ContentAwareIframe
    console.log('Undo:', lastChange)
  }

  const redo = () => {
    if (redoStack.length === 0) return
    
    const nextChange = redoStack[0]
    setRedoStack(prev => prev.slice(1))
    setUndoStack(prev => [nextChange, ...prev])
    
    // TODO: Implement redo functionality with ContentAwareIframe
    console.log('Redo:', nextChange)
  }

  const publishChanges = async () => {
    if (changeHistory.length === 0) {
      alert('No changes to publish')
      return
    }

    setIsSaving(true)
    try {
      const response = await fetch('/api/content/publish', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          changes: changeHistory
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Changes published successfully:', result)
        
        setDraftMode(false)
        setChangeHistory([])
        setUndoStack([])
        setRedoStack([])
        
        alert(`Successfully published ${changeHistory.length} changes!`)
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('Publish API Error:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error publishing changes:', error)
      alert(`Failed to publish changes: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSaving(false)
    }
  }

  if (!isClient) return null

  const selectedPageData = pages.find(p => p.id === selectedPage)

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Simple Top Toolbar */}
      <div className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-gray-900">Visual Editor</h1>
          <button
            onClick={interactiveMode ? disableInteractiveMode : enableInteractiveMode}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              interactiveMode
                ? 'bg-red-100 text-red-700 hover:bg-red-200'
                : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
            }`}
          >
            {interactiveMode ? 'Exit Edit Mode' : 'Edit Mode'}
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">

        {/* Live Preview */}
        <div className="flex-1 bg-gray-100 p-6">
          <div className="mx-auto bg-white shadow-lg rounded-lg overflow-hidden h-full">
            <ContentAwareIframe
              pagePath={previewUrl}
              onElementSelect={handleElementSelection}
              onContentLoad={(contentMap) => {
                console.log('Content loaded:', contentMap)
              }}
              interactiveMode={interactiveMode}
            />
          </div>
        </div>
      </div>

      {/* Edit Form Modal */}
      {selectedElement && (
        <div
          className="fixed bg-white border border-gray-300 rounded-lg shadow-lg p-4 z-50 min-w-80"
          style={{
            left: `${editFormPosition.x}px`,
            top: `${editFormPosition.y}px`,
            transform: 'translateX(-50%)'
          }}
        >
          <div className="mb-3">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Edit Text
            </label>
            <textarea
              value={editingValue}
              onChange={(e) => setEditingValue(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-sm"
              rows={3}
              placeholder="Enter new text..."
            />
          </div>

          {selectedElement.sourceLocation && (
            <div className="mb-3 text-xs text-gray-500">
              Source: {selectedElement.sourceLocation}
            </div>
          )}

          <div className="flex gap-2">
            <button
              onClick={saveElementEdit}
              disabled={isSaving || !editingValue.trim()}
              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
            >
              {isSaving ? 'Saving...' : 'Save'}
            </button>
            <button
              onClick={openTsxEditor}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              TSX Editor
            </button>
            <button
              onClick={() => setSelectedElement(null)}
              className="px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* TSX Editor Modal */}
      {showTsxEditor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">TSX File Editor</h2>
                {selectedTsxFile && highlightedText && (
                  <p className="text-sm text-green-600 mt-1">
                    ✓ Intelligent detection: Auto-selected file and highlighted text
                  </p>
                )}
              </div>
              <button
                onClick={() => setShowTsxEditor(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <XMarkIcon className="w-6 h-6" />
              </button>
            </div>

            <div className="flex-1 flex overflow-hidden">
              {/* File Content - Direct display of detected file */}
              <div className="flex-1 flex flex-col">
                {selectedTsxFile ? (
                  <>
                    <div className="p-4 border-b border-gray-200">
                      <h3 className="font-medium text-gray-900">{selectedTsxFile.filePath}</h3>
                      {highlightedText && (
                        <p className="text-sm text-gray-600 mt-1">
                          Detected file containing: "{highlightedText.text}" at line {highlightedText.startLine}
                        </p>
                      )}
                    </div>
                    <div className="flex-1 overflow-hidden">
                      <TsxFilePreview
                        content={selectedTsxFile.content}
                        filePath={selectedTsxFile.filePath}
                        onTextSelect={handleTextHighlight}
                        highlightedText={highlightedText}
                      />
                    </div>
                  </>
                ) : (
                  <div className="flex-1 flex flex-col items-center justify-center text-gray-500">
                    <DocumentTextIcon className="h-12 w-12 mb-4 text-gray-300" />
                    <p className="text-lg font-medium mb-2">No file detected</p>
                    <p className="text-sm text-center max-w-md">
                      Select text in the live preview first, then click TSX Editor to automatically detect and open the source file.
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* TSX Edit Form */}
            {highlightedText && (
              <div className="p-4 border-t border-gray-200 bg-gray-50">
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Edit Selected Text
                  </label>
                  <div className="text-xs text-gray-500 mb-2">
                    Line {highlightedText.startLine}, Column {highlightedText.startColumn}
                  </div>
                  <textarea
                    value={tsxEditingValue}
                    onChange={(e) => setTsxEditingValue(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    rows={3}
                    placeholder="Enter new text..."
                  />
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={saveTsxEdit}
                    disabled={isSaving || !tsxEditingValue.trim()}
                    className="px-4 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isSaving ? 'Saving...' : 'Save Changes'}
                  </button>
                  <button
                    onClick={() => {
                      setHighlightedText(null)
                      setTsxEditingValue('')
                    }}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default VisualEditor