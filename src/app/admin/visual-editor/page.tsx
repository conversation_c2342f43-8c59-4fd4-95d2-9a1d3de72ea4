'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import {
  HomeIcon,
  InformationCircleIcon,
  BriefcaseIcon,
  PhoneIcon,
  UserGroupIcon,
  DocumentTextIcon,
  EyeIcon,
  ArrowPathIcon,
  CursorArrowRaysIcon,
  PencilIcon,
  XMarkIcon,
  CheckIcon,
  ComputerDesktopIcon,
  MagnifyingGlassIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  DocumentDuplicateIcon,
  ClockIcon,
  CloudArrowUpIcon,
  EyeSlashIcon,
  PlusIcon,
  TrashIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  Cog6ToothIcon,
  GlobeAltIcon,
  DevicePhoneMobileIcon,
  DeviceTabletIcon,
  ComputerDesktopIcon as DesktopIcon
} from '@heroicons/react/24/outline'
import Toolbar from '@/components/admin/visual-editor/toolbar'
import Sidebar from '@/components/admin/visual-editor/sidebar'
import HistoryPanel from '@/components/admin/visual-editor/history-panel'
import EditModal from '@/components/admin/visual-editor/edit-modal'
import ContentAwareIframe from '@/components/admin/visual-editor/content-aware-iframe'
import TsxFilePreview from '@/components/admin/visual-editor/tsx-file-preview'

interface PageData {
  id: string
  name: string
  path: string
  description: string
  lastModified: string
  status: 'draft' | 'published'
}

interface EditableElement {
  id: string
  text: string
  description: string
  filePath: string
  line?: number
  rect: DOMRect
  className?: string
  elementPath?: string
  selector?: string
  tagName?: string
  sourceLocation?: string
  lineNumber?: number
  columnStart?: number
  elementInfo?: {
    type: 'getContent' | 'stats' | 'const' | 'general' | 'database'
    page?: string
    section?: string
    key?: string
    varName?: string
  }
}

interface ChangeHistory {
  id: string
  type: 'text' | 'style' | 'layout'
  description: string
  timestamp: Date
  elementId: string
  oldValue: string
  newValue: string
}

interface TsxFileData {
  filePath: string
  content: string
  lastModified: string
}

interface HighlightedText {
  id: string
  text: string
  startLine: number
  endLine: number
  startColumn: number
  endColumn: number
  filePath: string
}

function VisualEditor() {
  const { data: session, status } = useSession()
  const [selectedPage, setSelectedPage] = useState<string>('home')
  const [pages, setPages] = useState<PageData[]>([])
  const [previewUrl, setPreviewUrl] = useState('')
  const [interactiveMode, setInteractiveMode] = useState(false)
  const [iframeLoaded, setIframeLoaded] = useState(false)
  const [selectedElement, setSelectedElement] = useState<EditableElement | null>(null)
  const [editingValue, setEditingValue] = useState('')
  const [editFormPosition, setEditFormPosition] = useState({ x: 0, y: 0 })
  const [isClient, setIsClient] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [changeHistory, setChangeHistory] = useState<ChangeHistory[]>([])
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [draftMode, setDraftMode] = useState(false)
  const [undoStack, setUndoStack] = useState<ChangeHistory[]>([])
  const [redoStack, setRedoStack] = useState<ChangeHistory[]>([])

  // TSX File Editor States
  const [showTsxEditor, setShowTsxEditor] = useState(false)
  const [selectedTsxFile, setSelectedTsxFile] = useState<TsxFileData | null>(null)
  const [tsxFiles, setTsxFiles] = useState<TsxFileData[]>([])
  const [highlightedText, setHighlightedText] = useState<HighlightedText | null>(null)
  const [tsxSearchQuery, setTsxSearchQuery] = useState('')
  const [tsxEditingValue, setTsxEditingValue] = useState('')

  // iframeRef is no longer needed as we use ContentAwareIframe

  // Set client-side flag to prevent hydration issues
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (status === 'loading') return
    if (!session?.user || session.user.role !== 'ADMIN') {
      redirect('/auth/signin')
    }
  }, [session, status])

  // Load pages data
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.role === 'ADMIN') {
      loadPagesData()
      loadTsxFiles()
    }
  }, [session, status])

  // Initialize preview URL
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const baseUrl = `${window.location.protocol}//${window.location.host}`
      const page = pages.find(p => p.id === selectedPage)
      if (page) {
        setPreviewUrl(`${baseUrl}${page.path}`)
      } else {
        setPreviewUrl(`${baseUrl}/`)
      }
      setIframeLoaded(false)
    }
  }, [selectedPage, pages])

  // Content-aware iframe handles its own script injection
  useEffect(() => {
    console.log('Interactive mode changed:', interactiveMode);
  }, [interactiveMode])

  const loadPagesData = async () => {
    try {
      const response = await fetch('/api/content/pages', {
        credentials: 'include'
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.pages && data.pages.length > 0) {
          setPages(data.pages)
        } else {
          setPages(getFallbackPagesData())
        }
      } else {
        setPages(getFallbackPagesData())
      }
    } catch (error) {
      console.error('Error loading pages data:', error)
      setPages(getFallbackPagesData())
    }
  }

  const getFallbackPagesData = (): PageData[] => {
    return [
      {
        id: 'home',
        name: 'Home',
        path: '/',
        description: 'Main landing page',
        lastModified: new Date().toISOString(),
        status: 'published'
      },
      {
        id: 'about',
        name: 'About',
        path: '/about',
        description: 'Company information page',
        lastModified: new Date().toISOString(),
        status: 'published'
      },
      {
        id: 'services',
        name: 'Services',
        path: '/services',
        description: 'Services showcase',
        lastModified: new Date().toISOString(),
        status: 'published'
      },
      {
        id: 'contact',
        name: 'Contact',
        path: '/contact',
        description: 'Contact information',
        lastModified: new Date().toISOString(),
        status: 'published'
      },
      {
        id: 'team',
        name: 'Team',
        path: '/team',
        description: 'Team members',
        lastModified: new Date().toISOString(),
        status: 'published'
      }
    ]
  }

  const loadTsxFiles = async () => {
    try {
      const response = await fetch('/api/content/tsx-files', {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.files) {
          setTsxFiles(data.files)
        }
      }
    } catch (error) {
      console.error('Error loading TSX files:', error)
    }
  }

  const enableInteractiveMode = () => {
    console.log('Enabling interactive mode')
    setInteractiveMode(true)
  }

  const disableInteractiveMode = () => {
    console.log('Disabling interactive mode')
    setInteractiveMode(false)
  }

  const handleElementSelection = async (element: EditableElement) => {
    setSelectedElement(element)
    setEditingValue(element.text)
    setEditFormPosition({
      x: element.rect.left + element.rect.width / 2,
      y: element.rect.top - 10
    })
    
    // Find the source location of this text
    try {
      const response = await fetch(`/api/content/source-update?text=${encodeURIComponent(element.text)}&pagePath=${encodeURIComponent(element.filePath)}`, {
        credentials: 'include'
      })
      
      if (response.ok) {
        const result = await response.json()
        console.log('Text locations found:', result.data)
        
        if (result.data.locations && result.data.locations.length > 0) {
          // If multiple locations found, use the first one but show info about all
          const firstLocation = result.data.locations[0]
          const locationInfo = result.data.totalOccurrences > 1 
            ? `${firstLocation.locationDescription} (${result.data.totalOccurrences} occurrences found)`
            : firstLocation.locationDescription
          
          // Update the element with location information
          setSelectedElement({
            ...element,
            sourceLocation: locationInfo,
            filePath: firstLocation.filePath,
            lineNumber: firstLocation.lineNumber,
            columnStart: firstLocation.columnStart
          })
          
          // If multiple occurrences, show a warning
          if (result.data.totalOccurrences > 1) {
            console.log(`Found ${result.data.totalOccurrences} occurrences of "${element.text}":`)
            result.data.locations.forEach((loc: any, index: number) => {
              console.log(`${index + 1}. ${loc.locationDescription}`)
            })
          }
        } else {
          console.log('Could not find text location in source files')
        }
      } else {
        console.log('Could not find text location in source files')
      }
    } catch (error) {
      console.error('Error finding text location:', error)
    }
  }

    const saveElementEdit = async () => {
    if (!selectedElement || !editingValue.trim()) return

    setIsSaving(true)

    // Add to change history
    const change: ChangeHistory = {
      id: Date.now().toString(),
      type: 'text',
      description: `Updated text in ${selectedElement.description}`,
      timestamp: new Date(),
      elementId: selectedElement.id,
      oldValue: selectedElement.text,
      newValue: editingValue
    }

    setChangeHistory(prev => [change, ...prev])
    setUndoStack(prev => [change, ...prev])
    setRedoStack([])

    try {
      const requestBody = {
        oldText: selectedElement.text,
        newText: editingValue,
        pagePath: selectedElement.filePath,
        targetLine: Number(selectedElement.lineNumber), // Ensure it's a number
        targetFile: selectedElement.sourceLocation?.split(' (')[0] // Extract file path from location string
      }
      
      console.log('Sending source update request:', requestBody)
      
      const response = await fetch('/api/content/source-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody)
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Content updated successfully:', result)
        
        // Force refresh the iframe content
        console.log('Content updated successfully, refreshing iframe content');
        
        // Send message to iframe to update the specific element
        if (selectedElement) {
          console.log('Sending UPDATE_ELEMENT_TEXT message to iframe:', {
            elementId: selectedElement.id,
            newText: editingValue
          });
          const iframe = document.querySelector('iframe');
          if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage({
              type: 'UPDATE_ELEMENT_TEXT',
              elementId: selectedElement.id,
              newText: editingValue
            }, '*');
            console.log('Message sent to iframe');
          } else {
            console.log('Iframe not found or not ready');
          }
        }
        
        setSelectedElement(null)
        setEditingValue('')
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('API Error:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error saving element edit:', error)
      // You could add a toast notification here to show the error to the user
      alert(`Failed to save changes: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSaving(false)
    }
  }

  const handleTsxFileSelect = async (filePath: string) => {
    try {
      const response = await fetch(`/api/content/tsx-file-content?filePath=${encodeURIComponent(filePath)}`, {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setSelectedTsxFile({
            filePath,
            content: data.content,
            lastModified: data.lastModified
          })
        }
      }
    } catch (error) {
      console.error('Error loading TSX file content:', error)
    }
  }

  const handleTextHighlight = (selectedText: string, startLine: number, endLine: number, startColumn: number, endColumn: number) => {
    if (!selectedTsxFile) return

    const highlighted: HighlightedText = {
      id: Date.now().toString(),
      text: selectedText,
      startLine,
      endLine,
      startColumn,
      endColumn,
      filePath: selectedTsxFile.filePath
    }

    setHighlightedText(highlighted)
    setTsxEditingValue(selectedText)
  }

  const saveTsxEdit = async () => {
    if (!highlightedText || !tsxEditingValue.trim()) return

    setIsSaving(true)

    try {
      const response = await fetch('/api/content/tsx-edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          filePath: highlightedText.filePath,
          oldText: highlightedText.text,
          newText: tsxEditingValue,
          startLine: highlightedText.startLine,
          endLine: highlightedText.endLine,
          startColumn: highlightedText.startColumn,
          endColumn: highlightedText.endColumn
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('TSX file updated successfully:', result)

        // Reload the file content
        await handleTsxFileSelect(highlightedText.filePath)

        // Clear selection
        setHighlightedText(null)
        setTsxEditingValue('')

        // Add to change history
        const change: ChangeHistory = {
          id: Date.now().toString(),
          type: 'text',
          description: `Updated text in ${highlightedText.filePath}`,
          timestamp: new Date(),
          elementId: highlightedText.id,
          oldValue: highlightedText.text,
          newValue: tsxEditingValue
        }
        setChangeHistory(prev => [change, ...prev])

      } else {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error saving TSX edit:', error)
      alert(`Failed to save changes: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSaving(false)
    }
  }

  // injectInteractiveScript function removed - ContentAwareIframe handles script injection

  const handleIframeMessage = useCallback((event: MessageEvent) => {
    console.log('Received message from iframe:', event.data);
    if (event.data.type === 'ELEMENT_SELECTED') {
      console.log('Element selected:', event.data.element);
      handleElementSelection(event.data.element)
    }
  }, [])

  useEffect(() => {
    window.addEventListener('message', handleIframeMessage)
    return () => window.removeEventListener('message', handleIframeMessage)
  }, [handleIframeMessage])

  const undo = () => {
    if (undoStack.length === 0) return
    
    const lastChange = undoStack[0]
    setUndoStack(prev => prev.slice(1))
    setRedoStack(prev => [lastChange, ...prev])
    
    // TODO: Implement undo functionality with ContentAwareIframe
    console.log('Undo:', lastChange)
  }

  const redo = () => {
    if (redoStack.length === 0) return
    
    const nextChange = redoStack[0]
    setRedoStack(prev => prev.slice(1))
    setUndoStack(prev => [nextChange, ...prev])
    
    // TODO: Implement redo functionality with ContentAwareIframe
    console.log('Redo:', nextChange)
  }

  const publishChanges = async () => {
    if (changeHistory.length === 0) {
      alert('No changes to publish')
      return
    }

    setIsSaving(true)
    try {
      const response = await fetch('/api/content/publish', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          changes: changeHistory
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Changes published successfully:', result)
        
        setDraftMode(false)
        setChangeHistory([])
        setUndoStack([])
        setRedoStack([])
        
        alert(`Successfully published ${changeHistory.length} changes!`)
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('Publish API Error:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error publishing changes:', error)
      alert(`Failed to publish changes: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSaving(false)
    }
  }

  if (!isClient) return null

  const selectedPageData = pages.find(p => p.id === selectedPage)

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Left Sidebar */}
      <Sidebar
        pages={pages}
        selectedPage={selectedPage}
        onPageSelect={setSelectedPage}
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        showHistory={showHistory}
        onToggleHistory={() => setShowHistory(!showHistory)}
        changeHistory={changeHistory}
        onPublish={publishChanges}
        isSaving={isSaving}
        draftMode={draftMode}
        onToggleDraftMode={() => setDraftMode(!draftMode)}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Toolbar */}
        <Toolbar
          interactiveMode={interactiveMode}
          onToggleInteractiveMode={interactiveMode ? disableInteractiveMode : enableInteractiveMode}
          previewMode={previewMode}
          onPreviewModeChange={setPreviewMode}
          draftMode={draftMode}
          onToggleDraftMode={() => setDraftMode(!draftMode)}
          onUndo={undo}
          onRedo={redo}
          canUndo={undoStack.length > 0}
          canRedo={redoStack.length > 0}
          selectedPage={selectedPageData?.name}
          showTsxEditor={showTsxEditor}
          onToggleTsxEditor={() => setShowTsxEditor(!showTsxEditor)}
        />

        {/* Preview Area */}
        <div className="flex-1 flex h-full">
          {/* Main Preview */}
          <div className="flex-1 flex flex-col h-full">
            {!showTsxEditor ? (
              <div className="flex-1 bg-gray-100 p-6 h-full">
                <div className={`mx-auto bg-white shadow-lg rounded-lg overflow-hidden h-full ${
                  previewMode === 'desktop' ? 'w-full max-w-6xl' :
                  previewMode === 'tablet' ? 'w-full max-w-2xl' :
                  'w-full max-w-sm'
                }`}>
                  <ContentAwareIframe
                    pagePath={previewUrl}
                    onElementSelect={handleElementSelection}
                    onContentLoad={(contentMap) => {
                      console.log('Content loaded:', contentMap)
                    }}
                    interactiveMode={interactiveMode}
                  />
                  {/* Debug info */}
                  <div className="absolute top-2 right-2 bg-black text-white p-2 rounded text-xs">
                    Interactive Mode: {interactiveMode ? 'ON' : 'OFF'}
                  </div>
                </div>
              </div>
            ) : (
              /* TSX File Editor */
              <div className="flex-1 flex h-full">
                {/* File List */}
                <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
                  <div className="p-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">TSX Files</h3>
                    <p className="text-sm text-gray-500">Select a file to edit</p>
                  </div>

                  <div className="p-4">
                    <input
                      type="text"
                      placeholder="Search files..."
                      value={tsxSearchQuery}
                      onChange={(e) => setTsxSearchQuery(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div className="flex-1 overflow-y-auto">
                    {tsxFiles
                      .filter(file =>
                        file.filePath.toLowerCase().includes(tsxSearchQuery.toLowerCase())
                      )
                      .map((file) => (
                        <div
                          key={file.filePath}
                          onClick={() => handleTsxFileSelect(file.filePath)}
                          className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                            selectedTsxFile?.filePath === file.filePath ? 'bg-blue-50 border-blue-200' : ''
                          }`}
                        >
                          <div className="text-sm font-medium text-gray-900 truncate">
                            {file.filePath.split('/').pop()}
                          </div>
                          <div className="text-xs text-gray-500 truncate">
                            {file.filePath}
                          </div>
                          <div className="text-xs text-gray-400 mt-1">
                            {new Date(file.lastModified).toLocaleDateString()}
                          </div>
                        </div>
                      ))}
                  </div>
                </div>

                {/* File Content Preview */}
                <div className="flex-1 flex flex-col">
                  {selectedTsxFile ? (
                    <>
                      <div className="p-4 border-b border-gray-200 bg-gray-50">
                        <h4 className="text-sm font-semibold text-gray-900">
                          {selectedTsxFile.filePath}
                        </h4>
                        <p className="text-xs text-gray-500">
                          Select text to highlight and edit
                        </p>
                      </div>

                      <div className="flex-1 relative">
                        <TsxFilePreview
                          content={selectedTsxFile.content}
                          filePath={selectedTsxFile.filePath}
                          onTextSelect={handleTextHighlight}
                          highlightedText={highlightedText}
                        />
                      </div>
                    </>
                  ) : (
                    <div className="flex-1 flex items-center justify-center text-gray-500">
                      <div className="text-center">
                        <DocumentTextIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>Select a TSX file to preview</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Right Sidebar - History */}
          <HistoryPanel
            isVisible={showHistory}
            changeHistory={changeHistory}
            onUndo={(changeId) => {
              const change = changeHistory.find(c => c.id === changeId)
              if (change) {
                // Implement undo logic
                console.log('Undo change:', changeId)
              }
            }}
            onRedo={(changeId) => {
              const change = changeHistory.find(c => c.id === changeId)
              if (change) {
                // Implement redo logic
                console.log('Redo change:', changeId)
              }
            }}
            onClearHistory={() => setChangeHistory([])}
          />
        </div>
      </div>

      {/* Floating Edit Modal */}
      <EditModal
        element={selectedElement}
        value={editingValue}
        onChange={setEditingValue}
        onSave={saveElementEdit}
        onCancel={() => setSelectedElement(null)}
        isSaving={isSaving}
        position={editFormPosition}
      />

      {/* TSX Edit Modal */}
      {highlightedText && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Edit TSX Content</h3>
                <button
                  onClick={() => {
                    setHighlightedText(null)
                    setTsxEditingValue('')
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="mb-4">
                <div className="text-sm text-gray-600 mb-2">
                  File: {highlightedText.filePath}
                </div>
                <div className="text-sm text-gray-600 mb-4">
                  Lines {highlightedText.startLine}-{highlightedText.endLine}
                </div>

                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Original Text:
                </label>
                <div className="bg-gray-100 p-3 rounded border text-sm font-mono mb-4">
                  {highlightedText.text}
                </div>

                <label className="block text-sm font-medium text-gray-700 mb-2">
                  New Text:
                </label>
                <textarea
                  value={tsxEditingValue}
                  onChange={(e) => setTsxEditingValue(e.target.value)}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg text-sm font-mono focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={6}
                  placeholder="Enter your new content here..."
                />
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setHighlightedText(null)
                    setTsxEditingValue('')
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={saveTsxEdit}
                  disabled={isSaving || !tsxEditingValue.trim()}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 rounded-lg transition-colors flex items-center space-x-2"
                >
                  {isSaving && <ArrowPathIcon className="h-4 w-4 animate-spin" />}
                  <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default VisualEditor 