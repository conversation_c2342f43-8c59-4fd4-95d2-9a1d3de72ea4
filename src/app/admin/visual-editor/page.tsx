'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import {
  HomeIcon,
  InformationCircleIcon,
  BriefcaseIcon,
  PhoneIcon,
  UserGroupIcon,
  DocumentTextIcon,
  EyeIcon,
  ArrowPathIcon,
  CursorArrowRaysIcon,
  PencilIcon,
  XMarkIcon,
  CheckIcon,
  ComputerDesktopIcon,
  MagnifyingGlassIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  DocumentDuplicateIcon,
  ClockIcon,
  CloudArrowUpIcon,
  EyeSlashIcon,
  PlusIcon,
  TrashIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  Cog6ToothIcon,
  GlobeAltIcon,
  DevicePhoneMobileIcon,
  DeviceTabletIcon,
  ComputerDesktopIcon as DesktopIcon
} from '@heroicons/react/24/outline'
import Toolbar from '@/components/admin/visual-editor/toolbar'
import Sidebar from '@/components/admin/visual-editor/sidebar'
import HistoryPanel from '@/components/admin/visual-editor/history-panel'
import EditModal from '@/components/admin/visual-editor/edit-modal'
import ContentAwareIframe from '@/components/admin/visual-editor/content-aware-iframe'

interface PageData {
  id: string
  name: string
  path: string
  description: string
  lastModified: string
  status: 'draft' | 'published'
}

interface EditableElement {
  id: string
  text: string
  description: string
  filePath: string
  line?: number
  rect: DOMRect
  className?: string
  elementPath?: string
  selector?: string
  tagName?: string
  sourceLocation?: string
  lineNumber?: number
  columnStart?: number
  elementInfo?: {
    type: 'getContent' | 'stats' | 'const' | 'general' | 'database'
    page?: string
    section?: string
    key?: string
    varName?: string
  }
}

interface ChangeHistory {
  id: string
  type: 'text' | 'style' | 'layout'
  description: string
  timestamp: Date
  elementId: string
  oldValue: string
  newValue: string
}

function VisualEditor() {
  const { data: session, status } = useSession()
  const [selectedPage, setSelectedPage] = useState<string>('home')
  const [pages, setPages] = useState<PageData[]>([])
  const [previewUrl, setPreviewUrl] = useState('')
  const [interactiveMode, setInteractiveMode] = useState(false)
  const [iframeLoaded, setIframeLoaded] = useState(false)
  const [selectedElement, setSelectedElement] = useState<EditableElement | null>(null)
  const [editingValue, setEditingValue] = useState('')
  const [editFormPosition, setEditFormPosition] = useState({ x: 0, y: 0 })
  const [isClient, setIsClient] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [changeHistory, setChangeHistory] = useState<ChangeHistory[]>([])
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [draftMode, setDraftMode] = useState(false)
  const [undoStack, setUndoStack] = useState<ChangeHistory[]>([])
  const [redoStack, setRedoStack] = useState<ChangeHistory[]>([])

  // iframeRef is no longer needed as we use ContentAwareIframe

  // Set client-side flag to prevent hydration issues
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (status === 'loading') return
    if (!session?.user || session.user.role !== 'ADMIN') {
      redirect('/auth/signin')
    }
  }, [session, status])

  // Load pages data
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.role === 'ADMIN') {
      loadPagesData()
    }
  }, [session, status])

  // Initialize preview URL
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const baseUrl = `${window.location.protocol}//${window.location.host}`
      const page = pages.find(p => p.id === selectedPage)
      if (page) {
        setPreviewUrl(`${baseUrl}${page.path}`)
      } else {
        setPreviewUrl(`${baseUrl}/`)
      }
      setIframeLoaded(false)
    }
  }, [selectedPage, pages])

  // Content-aware iframe handles its own script injection
  useEffect(() => {
    console.log('Interactive mode changed:', interactiveMode);
  }, [interactiveMode])

  const loadPagesData = async () => {
    try {
      const response = await fetch('/api/content/pages', {
        credentials: 'include'
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.pages && data.pages.length > 0) {
          setPages(data.pages)
        } else {
          setPages(getFallbackPagesData())
        }
      } else {
        setPages(getFallbackPagesData())
      }
    } catch (error) {
      console.error('Error loading pages data:', error)
      setPages(getFallbackPagesData())
    }
  }

  const getFallbackPagesData = (): PageData[] => {
    return [
      {
        id: 'home',
        name: 'Home',
        path: '/',
        description: 'Main landing page',
        lastModified: new Date().toISOString(),
        status: 'published'
      },
      {
        id: 'about',
        name: 'About',
        path: '/about',
        description: 'Company information page',
        lastModified: new Date().toISOString(),
        status: 'published'
      },
      {
        id: 'services',
        name: 'Services',
        path: '/services',
        description: 'Services showcase',
        lastModified: new Date().toISOString(),
        status: 'published'
      },
      {
        id: 'contact',
        name: 'Contact',
        path: '/contact',
        description: 'Contact information',
        lastModified: new Date().toISOString(),
        status: 'published'
      },
      {
        id: 'team',
        name: 'Team',
        path: '/team',
        description: 'Team members',
        lastModified: new Date().toISOString(),
        status: 'published'
      }
    ]
  }

  const enableInteractiveMode = () => {
    console.log('Enabling interactive mode')
    setInteractiveMode(true)
  }

  const disableInteractiveMode = () => {
    console.log('Disabling interactive mode')
    setInteractiveMode(false)
  }

  const handleElementSelection = async (element: EditableElement) => {
    setSelectedElement(element)
    setEditingValue(element.text)
    setEditFormPosition({
      x: element.rect.left + element.rect.width / 2,
      y: element.rect.top - 10
    })
    
    // Find the source location of this text
    try {
      const response = await fetch(`/api/content/source-update?text=${encodeURIComponent(element.text)}&pagePath=${encodeURIComponent(element.filePath)}`, {
        credentials: 'include'
      })
      
      if (response.ok) {
        const result = await response.json()
        console.log('Text locations found:', result.data)
        
        if (result.data.locations && result.data.locations.length > 0) {
          // If multiple locations found, use the first one but show info about all
          const firstLocation = result.data.locations[0]
          const locationInfo = result.data.totalOccurrences > 1 
            ? `${firstLocation.locationDescription} (${result.data.totalOccurrences} occurrences found)`
            : firstLocation.locationDescription
          
          // Update the element with location information
          setSelectedElement({
            ...element,
            sourceLocation: locationInfo,
            filePath: firstLocation.filePath,
            lineNumber: firstLocation.lineNumber,
            columnStart: firstLocation.columnStart
          })
          
          // If multiple occurrences, show a warning
          if (result.data.totalOccurrences > 1) {
            console.log(`Found ${result.data.totalOccurrences} occurrences of "${element.text}":`)
            result.data.locations.forEach((loc: any, index: number) => {
              console.log(`${index + 1}. ${loc.locationDescription}`)
            })
          }
        } else {
          console.log('Could not find text location in source files')
        }
      } else {
        console.log('Could not find text location in source files')
      }
    } catch (error) {
      console.error('Error finding text location:', error)
    }
  }

    const saveElementEdit = async () => {
    if (!selectedElement || !editingValue.trim()) return

    setIsSaving(true)

    // Add to change history
    const change: ChangeHistory = {
      id: Date.now().toString(),
      type: 'text',
      description: `Updated text in ${selectedElement.description}`,
      timestamp: new Date(),
      elementId: selectedElement.id,
      oldValue: selectedElement.text,
      newValue: editingValue
    }

    setChangeHistory(prev => [change, ...prev])
    setUndoStack(prev => [change, ...prev])
    setRedoStack([])

    try {
      const requestBody = {
        oldText: selectedElement.text,
        newText: editingValue,
        pagePath: selectedElement.filePath,
        targetLine: Number(selectedElement.lineNumber), // Ensure it's a number
        targetFile: selectedElement.sourceLocation?.split(' (')[0] // Extract file path from location string
      }
      
      console.log('Sending source update request:', requestBody)
      
      const response = await fetch('/api/content/source-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody)
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Content updated successfully:', result)
        
        // Force refresh the iframe content
        console.log('Content updated successfully, refreshing iframe content');
        
        // Send message to iframe to update the specific element
        if (selectedElement) {
          console.log('Sending UPDATE_ELEMENT_TEXT message to iframe:', {
            elementId: selectedElement.id,
            newText: editingValue
          });
          const iframe = document.querySelector('iframe');
          if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage({
              type: 'UPDATE_ELEMENT_TEXT',
              elementId: selectedElement.id,
              newText: editingValue
            }, '*');
            console.log('Message sent to iframe');
          } else {
            console.log('Iframe not found or not ready');
          }
        }
        
        setSelectedElement(null)
        setEditingValue('')
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('API Error:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error saving element edit:', error)
      // You could add a toast notification here to show the error to the user
      alert(`Failed to save changes: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSaving(false)
    }
  }

  // injectInteractiveScript function removed - ContentAwareIframe handles script injection

  const handleIframeMessage = useCallback((event: MessageEvent) => {
    console.log('Received message from iframe:', event.data);
    if (event.data.type === 'ELEMENT_SELECTED') {
      console.log('Element selected:', event.data.element);
      handleElementSelection(event.data.element)
    }
  }, [])

  useEffect(() => {
    window.addEventListener('message', handleIframeMessage)
    return () => window.removeEventListener('message', handleIframeMessage)
  }, [handleIframeMessage])

  const undo = () => {
    if (undoStack.length === 0) return
    
    const lastChange = undoStack[0]
    setUndoStack(prev => prev.slice(1))
    setRedoStack(prev => [lastChange, ...prev])
    
    // TODO: Implement undo functionality with ContentAwareIframe
    console.log('Undo:', lastChange)
  }

  const redo = () => {
    if (redoStack.length === 0) return
    
    const nextChange = redoStack[0]
    setRedoStack(prev => prev.slice(1))
    setUndoStack(prev => [nextChange, ...prev])
    
    // TODO: Implement redo functionality with ContentAwareIframe
    console.log('Redo:', nextChange)
  }

  const publishChanges = async () => {
    if (changeHistory.length === 0) {
      alert('No changes to publish')
      return
    }

    setIsSaving(true)
    try {
      const response = await fetch('/api/content/publish', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          changes: changeHistory
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Changes published successfully:', result)
        
        setDraftMode(false)
        setChangeHistory([])
        setUndoStack([])
        setRedoStack([])
        
        alert(`Successfully published ${changeHistory.length} changes!`)
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('Publish API Error:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error publishing changes:', error)
      alert(`Failed to publish changes: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSaving(false)
    }
  }

  if (!isClient) return null

  const selectedPageData = pages.find(p => p.id === selectedPage)

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Left Sidebar */}
      <Sidebar
        pages={pages}
        selectedPage={selectedPage}
        onPageSelect={setSelectedPage}
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        showHistory={showHistory}
        onToggleHistory={() => setShowHistory(!showHistory)}
        changeHistory={changeHistory}
        onPublish={publishChanges}
        isSaving={isSaving}
        draftMode={draftMode}
        onToggleDraftMode={() => setDraftMode(!draftMode)}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Toolbar */}
        <Toolbar
          interactiveMode={interactiveMode}
          onToggleInteractiveMode={interactiveMode ? disableInteractiveMode : enableInteractiveMode}
          previewMode={previewMode}
          onPreviewModeChange={setPreviewMode}
          draftMode={draftMode}
          onToggleDraftMode={() => setDraftMode(!draftMode)}
          onUndo={undo}
          onRedo={redo}
          canUndo={undoStack.length > 0}
          canRedo={redoStack.length > 0}
          selectedPage={selectedPageData?.name}
        />

        {/* Preview Area */}
        <div className="flex-1 flex h-full">
          {/* Main Preview */}
          <div className="flex-1 flex flex-col h-full">
            <div className="flex-1 bg-gray-100 p-6 h-full">
              <div className={`mx-auto bg-white shadow-lg rounded-lg overflow-hidden h-full ${
                previewMode === 'desktop' ? 'w-full max-w-6xl' :
                previewMode === 'tablet' ? 'w-full max-w-2xl' :
                'w-full max-w-sm'
              }`}>
                        <ContentAwareIframe
          pagePath={previewUrl}
          onElementSelect={handleElementSelection}
          onContentLoad={(contentMap) => {
            console.log('Content loaded:', contentMap)
          }}
          interactiveMode={interactiveMode}
        />
        {/* Debug info */}
        <div className="absolute top-2 right-2 bg-black text-white p-2 rounded text-xs">
          Interactive Mode: {interactiveMode ? 'ON' : 'OFF'}
        </div>
              </div>
            </div>
          </div>

          {/* Right Sidebar - History */}
          <HistoryPanel
            isVisible={showHistory}
            changeHistory={changeHistory}
            onUndo={(changeId) => {
              const change = changeHistory.find(c => c.id === changeId)
              if (change) {
                // Implement undo logic
                console.log('Undo change:', changeId)
              }
            }}
            onRedo={(changeId) => {
              const change = changeHistory.find(c => c.id === changeId)
              if (change) {
                // Implement redo logic
                console.log('Redo change:', changeId)
              }
            }}
            onClearHistory={() => setChangeHistory([])}
          />
        </div>
      </div>

      {/* Floating Edit Modal */}
      <EditModal
        element={selectedElement}
        value={editingValue}
        onChange={setEditingValue}
        onSave={saveElementEdit}
        onCancel={() => setSelectedElement(null)}
        isSaving={isSaving}
        position={editFormPosition}
      />
    </div>
  )
}

export default VisualEditor 