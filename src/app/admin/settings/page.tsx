'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useSession } from 'next-auth/react'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CogIcon,
  Cog6ToothIcon,
  UserIcon,
  UserCircleIcon,
  BellIcon,
  ShieldCheckIcon,
  ShieldExclamationIcon,
  PaintBrushIcon,
  LinkIcon,
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  Squares2X2Icon,
  ListBulletIcon,
  CloudIcon,
  GlobeAltIcon,
  DocumentTextIcon,
  DocumentIcon,
  KeyIcon,
  EnvelopeIcon,
  BookmarkIcon,
  ServerIcon,
  HomeIcon,
  PhoneIcon,
  LockClosedIcon,
  ChartBarIcon,
  ChartBarSquareIcon,
  CreditCardIcon,
  ChatBubbleLeftRightIcon,
  ChatBubbleOvalLeftIcon,
  DevicePhoneMobileIcon,
  DeviceTabletIcon,
  ShareIcon,
  HeartIcon,
  BanknotesIcon,
  CurrencyDollarIcon,
  ReceiptPercentIcon,
  ArrowPathIcon,
  ArrowPathRoundedSquareIcon,
  ArrowDownTrayIcon,
  SwatchIcon,
  MapPinIcon,
  ClockIcon,
  FlagIcon,
  CircleStackIcon,
  PhotoIcon,
  VideoCameraIcon,
  CodeBracketIcon,
  BoltIcon,
  CpuChipIcon,
  BuildingOfficeIcon,
  MegaphoneIcon,
  StarIcon,
  QuestionMarkCircleIcon,
  BookOpenIcon,
  CheckCircleIcon,
  BeakerIcon,
  RocketLaunchIcon,
  NewspaperIcon,
  LanguageIcon,
  CalendarIcon,
  ShoppingBagIcon,
  ShoppingCartIcon,
  ArchiveBoxIcon,
} from '@heroicons/react/24/outline'
import Select from 'react-select'
import CreatableSelect from 'react-select/creatable'

// Types
interface Setting {
  id: string
  key: string
  value: string
  description?: string
  category: string
  fieldtype: string
  options?: string
  isactive: boolean
  ispublic: boolean
  createdat: string
  updatedat?: string
  categorystyle?: any
}

interface SettingFormData {
  key: string
  value: string
  description: string
  category: string
  fieldType: string
  options: string
  isactive: boolean
  ispublic: boolean
  newCategoryLabel?: string
}

interface Category {
  name: string
  label: string
  icon: any
  color: string
  description: string
  order: number
  categorystyle?: any
}

// Icon mapping for categories
const ICON_MAP: Record<string, typeof CogIcon> = {
  'CogIcon': CogIcon,
  'Cog6ToothIcon': Cog6ToothIcon,
  'UserIcon': UserIcon,
  'UserCircleIcon': UserCircleIcon,
  'BellIcon': BellIcon,
  'ShieldCheckIcon': ShieldCheckIcon,
  'ShieldExclamationIcon': ShieldExclamationIcon,
  'LinkIcon': LinkIcon,
  'PaintBrushIcon': PaintBrushIcon,
  'ServerIcon': ServerIcon,
  'DocumentTextIcon': DocumentTextIcon,
  'DocumentIcon': DocumentIcon,
  'HomeIcon': HomeIcon,
  'PhoneIcon': PhoneIcon,
  'GlobeAltIcon': GlobeAltIcon,
  'EyeIcon': EyeIcon,
  'LockClosedIcon': LockClosedIcon,
  'ChartBarIcon': ChartBarIcon,
  'ChartBarSquareIcon': ChartBarSquareIcon,
  'CloudIcon': CloudIcon,
  'CreditCardIcon': CreditCardIcon,
  'EnvelopeIcon': EnvelopeIcon,
  'ChatBubbleLeftRightIcon': ChatBubbleLeftRightIcon,
  'ChatBubbleOvalLeftIcon': ChatBubbleOvalLeftIcon,
  'DevicePhoneMobileIcon': DevicePhoneMobileIcon,
  'DeviceTabletIcon': DeviceTabletIcon,
  'ShareIcon': ShareIcon,
  'HeartIcon': HeartIcon,
  'BanknotesIcon': BanknotesIcon,
  'CurrencyDollarIcon': CurrencyDollarIcon,
  'ReceiptPercentIcon': ReceiptPercentIcon,
  'ArrowPathIcon': ArrowPathIcon,
  'ArrowPathRoundedSquareIcon': ArrowPathRoundedSquareIcon,
  'ArrowDownTrayIcon': ArrowDownTrayIcon,
  'SwatchIcon': SwatchIcon,
  'MapPinIcon': MapPinIcon,
  'ClockIcon': ClockIcon,
  'FlagIcon': FlagIcon,
  'CircleStackIcon': CircleStackIcon,
  'PhotoIcon': PhotoIcon,
  'VideoCameraIcon': VideoCameraIcon,
  'CodeBracketIcon': CodeBracketIcon,
  'BoltIcon': BoltIcon,
  'CpuChipIcon': CpuChipIcon,
  'BuildingOfficeIcon': BuildingOfficeIcon,
  'MegaphoneIcon': MegaphoneIcon,
  'StarIcon': StarIcon,
  'QuestionMarkCircleIcon': QuestionMarkCircleIcon,
  'BookOpenIcon': BookOpenIcon,
  'CheckCircleIcon': CheckCircleIcon,
  'BeakerIcon': BeakerIcon,
  'RocketLaunchIcon': RocketLaunchIcon,
  'NewspaperIcon': NewspaperIcon,
  'LanguageIcon': LanguageIcon,
  'CalendarIcon': CalendarIcon,
  'ShoppingBagIcon': ShoppingBagIcon,
  'ShoppingCartIcon': ShoppingCartIcon,
  'ArchiveBoxIcon': ArchiveBoxIcon,
}

// Available icons for category selection
const AVAILABLE_ICONS = [
  { name: 'CogIcon', label: 'Settings', icon: CogIcon },
  { name: 'Cog6ToothIcon', label: 'Configuration', icon: Cog6ToothIcon },
  { name: 'UserIcon', label: 'User', icon: UserIcon },
  { name: 'UserCircleIcon', label: 'Profile', icon: UserCircleIcon },
  { name: 'BellIcon', label: 'Notifications', icon: BellIcon },
  { name: 'ShieldCheckIcon', label: 'Security', icon: ShieldCheckIcon },
  { name: 'ShieldExclamationIcon', label: 'Protection', icon: ShieldExclamationIcon },
  { name: 'LinkIcon', label: 'Integrations', icon: LinkIcon },
  { name: 'PaintBrushIcon', label: 'Appearance', icon: PaintBrushIcon },
  { name: 'ServerIcon', label: 'System', icon: ServerIcon },
  { name: 'DocumentTextIcon', label: 'Documents', icon: DocumentTextIcon },
  { name: 'DocumentIcon', label: 'Files', icon: DocumentIcon },
  { name: 'HomeIcon', label: 'Home', icon: HomeIcon },
  { name: 'PhoneIcon', label: 'Contact', icon: PhoneIcon },
  { name: 'GlobeAltIcon', label: 'Global', icon: GlobeAltIcon },
  { name: 'EyeIcon', label: 'Visibility', icon: EyeIcon },
  { name: 'LockClosedIcon', label: 'Privacy', icon: LockClosedIcon },
  { name: 'ChartBarIcon', label: 'Analytics', icon: ChartBarIcon },
  { name: 'ChartBarSquareIcon', label: 'Reports', icon: ChartBarSquareIcon },
  { name: 'CloudIcon', label: 'Cloud', icon: CloudIcon },
  { name: 'CreditCardIcon', label: 'Payment', icon: CreditCardIcon },
  { name: 'EnvelopeIcon', label: 'Email', icon: EnvelopeIcon },
  { name: 'ChatBubbleLeftRightIcon', label: 'Chat', icon: ChatBubbleLeftRightIcon },
  { name: 'ChatBubbleOvalLeftIcon', label: 'Feedback', icon: ChatBubbleOvalLeftIcon },
  { name: 'DevicePhoneMobileIcon', label: 'Mobile', icon: DevicePhoneMobileIcon },
  { name: 'DeviceTabletIcon', label: 'Tablet', icon: DeviceTabletIcon },
  { name: 'ShareIcon', label: 'Share', icon: ShareIcon },
  { name: 'HeartIcon', label: 'Social', icon: HeartIcon },
  { name: 'BanknotesIcon', label: 'Money', icon: BanknotesIcon },
  { name: 'CurrencyDollarIcon', label: 'Currency', icon: CurrencyDollarIcon },
  { name: 'ReceiptPercentIcon', label: 'Tax', icon: ReceiptPercentIcon },
  { name: 'ArrowPathIcon', label: 'Backup', icon: ArrowPathIcon },
  { name: 'ArrowPathRoundedSquareIcon', label: 'Webhook', icon: ArrowPathRoundedSquareIcon },
  { name: 'ArrowDownTrayIcon', label: 'Download', icon: ArrowDownTrayIcon },
  { name: 'SwatchIcon', label: 'Color', icon: SwatchIcon },
  { name: 'MapPinIcon', label: 'Location', icon: MapPinIcon },
  { name: 'ClockIcon', label: 'Time', icon: ClockIcon },
  { name: 'FlagIcon', label: 'Country', icon: FlagIcon },
  { name: 'CircleStackIcon', label: 'Database', icon: CircleStackIcon },
  { name: 'PhotoIcon', label: 'Image', icon: PhotoIcon },
  { name: 'VideoCameraIcon', label: 'Video', icon: VideoCameraIcon },
  { name: 'CodeBracketIcon', label: 'Code', icon: CodeBracketIcon },
  { name: 'BoltIcon', label: 'Performance', icon: BoltIcon },
  { name: 'CpuChipIcon', label: 'Cache', icon: CpuChipIcon },
  { name: 'BuildingOfficeIcon', label: 'Business', icon: BuildingOfficeIcon },
  { name: 'MegaphoneIcon', label: 'Marketing', icon: MegaphoneIcon },
  { name: 'StarIcon', label: 'Brand', icon: StarIcon },
  { name: 'QuestionMarkCircleIcon', label: 'Support', icon: QuestionMarkCircleIcon },
  { name: 'BookOpenIcon', label: 'Knowledge', icon: BookOpenIcon },
  { name: 'CheckCircleIcon', label: 'Compliance', icon: CheckCircleIcon },
  { name: 'BeakerIcon', label: 'Testing', icon: BeakerIcon },
  { name: 'RocketLaunchIcon', label: 'Deployment', icon: RocketLaunchIcon },
  { name: 'NewspaperIcon', label: 'News', icon: NewspaperIcon },
  { name: 'LanguageIcon', label: 'Language', icon: LanguageIcon },
  { name: 'CalendarIcon', label: 'Calendar', icon: CalendarIcon },
  { name: 'ShoppingBagIcon', label: 'Shop', icon: ShoppingBagIcon },
  { name: 'ShoppingCartIcon', label: 'Cart', icon: ShoppingCartIcon },
  { name: 'ArchiveBoxIcon', label: 'Inventory', icon: ArchiveBoxIcon },
]

const FIELD_TYPES = [
  { value: 'text', label: 'Text Input', width: 'max-w-md' },
  { value: 'textarea', label: 'Text Area', width: 'max-w-lg' },
  { value: 'number', label: 'Number', width: 'w-32' },
  { value: 'email', label: 'Email', width: 'max-w-md' },
  { value: 'url', label: 'URL', width: 'max-w-lg' },
  { value: 'password', label: 'Password', width: 'max-w-sm' },
  { value: 'color', label: 'Color Picker', width: 'w-16' },
  { value: 'date', label: 'Date', width: 'w-40' },
  { value: 'time', label: 'Time', width: 'w-32' },
  { value: 'checkbox', label: 'Checkbox', width: 'w-auto' },
  { value: 'toggle', label: 'Toggle Switch', width: 'w-auto' },
  { value: 'dropdown', label: 'Dropdown', width: 'max-w-sm' },
  { value: 'radio', label: 'Radio Buttons', width: 'w-auto' },
  { value: 'file', label: 'File Upload', width: 'max-w-md' },
  { value: 'range', label: 'Range Slider', width: 'max-w-sm' },
]

// Separate component for field editing with local state
const FieldEditor = ({ setting, updateSettingValue, forceLongInput = false }: { setting: Setting, updateSettingValue: (setting: Setting, value: string) => Promise<void>, forceLongInput?: boolean }) => {
  const { fieldtype, value, options } = setting
  const fieldConfig = FIELD_TYPES.find(ft => ft.value === fieldtype)
  let fieldWidth = fieldConfig?.width || 'max-w-md'
  if (forceLongInput && !['color','date','time','checkbox','toggle','radio'].includes(fieldtype)) {
    fieldWidth = 'w-full'
  }

  // Local state for editing values without auto-save
  const [localValue, setLocalValue] = useState(value)
  const [hasLocalChanges, setHasLocalChanges] = useState(false)

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(value)
    setHasLocalChanges(false)
  }, [value])

  const handleValueChange = (newValue: string) => {
    setLocalValue(newValue)
    setHasLocalChanges(newValue !== value)
  }

  const saveLocalChanges = async () => {
    if (hasLocalChanges) {
      await updateSettingValue(setting, localValue)
      setHasLocalChanges(false)
    }
  }

  const baseInputClasses = `
    mt-1 block border border-gray-300 rounded-lg shadow-sm py-2.5 px-3.5
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    text-sm transition-all duration-200 ${fieldWidth}
  `.trim()

  const enhancedClasses = `
    ${baseInputClasses}
    placeholder-gray-400 text-gray-900
  `.trim()

  switch (fieldtype) {
    case 'checkbox':
      return (
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={localValue === 'true'}
              onChange={(e) => handleValueChange(e.target.checked.toString())}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors"
            />
            <span className={`ml-3 text-sm font-medium ${
              localValue === 'true' ? 'text-green-700' : 'text-gray-500'
            }`}>
              {localValue === 'true' ? 'Enabled' : 'Disabled'}
            </span>
          </div>
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="ml-3 px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'toggle':
      return (
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center">
            <button
              type="button"
              onClick={() => handleValueChange((localValue !== 'true').toString())}
              className={`${
                localValue === 'true' ? 'bg-blue-600' : 'bg-gray-200'
              } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full
              border-2 border-transparent transition-colors duration-200 ease-in-out
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
              hover:shadow-md`}
            >
              <span
                className={`${
                  localValue === 'true' ? 'translate-x-5' : 'translate-x-0'
                } pointer-events-none inline-block h-5 w-5 transform rounded-full
                bg-white shadow ring-0 transition duration-200 ease-in-out`}
              />
            </button>
            <span className={`ml-3 text-sm font-medium ${
              localValue === 'true' ? 'text-green-700' : 'text-gray-500'
            }`}>
              {localValue === 'true' ? 'Enabled' : 'Disabled'}
            </span>
          </div>
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="ml-3 px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'dropdown':
      const optionsList = options?.split(',').map(opt => opt.trim()).filter(opt => opt) || []
      return (
        <div className="flex items-center space-x-2">
          <select
            value={localValue}
            onChange={(e) => handleValueChange(e.target.value)}
            className={enhancedClasses}
          >
            <option value="">Select an option</option>
            {optionsList.map((option, index) => (
              <option key={index} value={option}>
                {option}
              </option>
            ))}
          </select>
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'textarea':
      return (
        <div className="space-y-2">
          <textarea
            value={localValue}
            onChange={(e) => handleValueChange(e.target.value)}
            rows={3}
            className={`${enhancedClasses} resize-none`}
            placeholder="Enter value..."
          />
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'number':
      return (
        <div className="flex items-center space-x-2">
          <input
            type="number"
            value={localValue}
            onChange={(e) => handleValueChange(e.target.value)}
            className={enhancedClasses}
            placeholder="0"
          />
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'email':
      return (
        <div className="flex items-center space-x-2">
          <input
            type="email"
            value={localValue}
            onChange={(e) => handleValueChange(e.target.value)}
            className={enhancedClasses}
            placeholder="<EMAIL>"
          />
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'url':
      return (
        <div className="flex items-center space-x-2">
          <input
            type="url"
            value={localValue}
            onChange={(e) => handleValueChange(e.target.value)}
            className={enhancedClasses}
            placeholder="https://example.com"
          />
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'password':
      return (
        <div className="flex items-center space-x-2">
          <input
            type="password"
            value={localValue}
            onChange={(e) => handleValueChange(e.target.value)}
            className={enhancedClasses}
            placeholder="••••••••"
          />
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'color':
      return (
        <div className="flex items-center space-x-3 mt-1">
          <input
            type="color"
            value={localValue || '#000000'}
            onChange={(e) => handleValueChange(e.target.value)}
            className="w-12 h-10 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
          />
          <span className="text-sm text-gray-600 font-mono">{localValue || '#000000'}</span>
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'date':
      return (
        <div className="flex items-center space-x-2">
          <input
            type="date"
            value={localValue}
            onChange={(e) => handleValueChange(e.target.value)}
            className={enhancedClasses}
          />
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'time':
      return (
        <div className="flex items-center space-x-2">
          <input
            type="time"
            value={localValue}
            onChange={(e) => handleValueChange(e.target.value)}
            className={enhancedClasses}
          />
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'radio':
      const radioOptions = options?.split(',').map(opt => opt.trim()).filter(opt => opt) || []
      return (
        <div className="mt-2 space-y-2">
          <div className="space-y-2">
            {radioOptions.map((option, index) => (
              <label key={index} className="flex items-center">
                <input
                  type="radio"
                  name={`setting-${setting.id}`}
                  value={option}
                  checked={localValue === option}
                  onChange={(e) => handleValueChange(e.target.value)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">{option}</span>
              </label>
            ))}
          </div>
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'file':
      return (
        <div className="mt-1 space-y-2">
          <input
            type="file"
            onChange={(e) => {
              const file = e.target.files?.[0]
              if (file) {
                handleValueChange(file.name)
              }
            }}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 transition-colors"
          />
          {localValue && (
            <p className="mt-1 text-xs text-gray-500">Current: {localValue}</p>
          )}
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    case 'range':
      const min = options?.split(',')[0] || '0'
      const max = options?.split(',')[1] || '100'
      const step = options?.split(',')[2] || '1'
      return (
        <div className="mt-1 space-y-2">
          <input
            type="range"
            min={min}
            max={max}
            step={step}
            value={localValue || min}
            onChange={(e) => handleValueChange(e.target.value)}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>{min}</span>
            <span className="font-medium text-blue-600">{localValue || min}</span>
            <span>{max}</span>
          </div>
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )

    default:
      return (
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={localValue}
            onChange={(e) => handleValueChange(e.target.value)}
            className={enhancedClasses}
            placeholder="Enter value..."
          />
          {hasLocalChanges && (
            <button
              onClick={saveLocalChanges}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save
            </button>
          )}
        </div>
      )
  }
}

export default function SettingsPage() {
  // Session and authentication
  const { data: session, status } = useSession()
  
  // Core state management
  const [settings, setSettings] = useState<Setting[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [activeCategory, setActiveCategory] = useState('')
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // UI state
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')
  const [showInactive, setShowInactive] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'warning'; text: string } | null>(null)

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [editingSetting, setEditingSetting] = useState<Setting | null>(null)
  const [formData, setFormData] = useState<SettingFormData>({
    key: '',
    value: '',
    description: '',
    category: 'GENERAL',
    fieldType: 'text',
    options: '',
    isactive: true,
    ispublic: true,
    newCategoryLabel: '',
  })

  // Category management state
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null)

  // Add at the top of the SettingsPage component:
  const [showDescId, setShowDescId] = useState<string | null>(null);
  const descTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleShowDesc = (id: string) => {
    setShowDescId(id);
    if (descTimeoutRef.current) clearTimeout(descTimeoutRef.current);
    descTimeoutRef.current = setTimeout(() => setShowDescId(null), 4000);
  };
  const handleHideDesc = () => {
    setShowDescId(null);
    if (descTimeoutRef.current) clearTimeout(descTimeoutRef.current);
  };

  // Fetch categories from database
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/settings/categories')
      const result = await response.json()

      console.log('Fetched categories response:', result)

      if (result.success && result.data) {
        const categoriesWithIcons = result.data.map((cat: any) => {
          // If no icon is specified, try to infer one based on category name
          let iconName = cat.icon
          const categoryLower = cat.name.toLowerCase()
          if (!iconName || iconName === 'DocumentTextIcon') {
            if (categoryLower.includes('general')) {
              iconName = 'CogIcon'
            } else if (categoryLower.includes('social')) {
              iconName = 'GlobeAltIcon'
            } else if (categoryLower.includes('user') || categoryLower.includes('auth')) {
              iconName = 'UserIcon'
            } else if (categoryLower.includes('email') || categoryLower.includes('mail')) {
              iconName = 'EnvelopeIcon'
            } else if (categoryLower.includes('payment') || categoryLower.includes('billing')) {
              iconName = 'CreditCardIcon'
            } else if (categoryLower.includes('security') || categoryLower.includes('privacy')) {
              iconName = 'ShieldCheckIcon'
            } else if (categoryLower.includes('notification') || categoryLower.includes('alert')) {
              iconName = 'BellIcon'
            } else if (categoryLower.includes('appearance') || categoryLower.includes('theme')) {
              iconName = 'PaintBrushIcon'
            } else if (categoryLower.includes('system') || categoryLower.includes('server')) {
              iconName = 'ServerIcon'
            } else if (categoryLower.includes('contact') || categoryLower.includes('phone')) {
              iconName = 'PhoneIcon'
            } else if (categoryLower.includes('global') || categoryLower.includes('world')) {
              iconName = 'GlobeAltIcon'
            } else if (categoryLower.includes('analytics') || categoryLower.includes('chart')) {
              iconName = 'ChartBarIcon'
            } else if (categoryLower.includes('cloud') || categoryLower.includes('storage')) {
              iconName = 'CloudIcon'
            } else if (categoryLower.includes('chat') || categoryLower.includes('message')) {
              iconName = 'ChatBubbleLeftRightIcon'
            } else if (categoryLower.includes('link') || categoryLower.includes('integration')) {
              iconName = 'LinkIcon'
            } else {
              iconName = 'DocumentTextIcon'
            }
          }
          
          // Also infer appropriate colors for common categories
          let color = cat.color
          if (!color || color === 'gray') {
            if (categoryLower.includes('general')) {
              color = 'blue'
            } else if (categoryLower.includes('social')) {
              color = 'purple'
            } else if (categoryLower.includes('security')) {
              color = 'red'
            } else if (categoryLower.includes('payment')) {
              color = 'green'
            } else if (categoryLower.includes('notification')) {
              color = 'yellow'
            } else if (categoryLower.includes('appearance')) {
              color = 'pink'
            } else if (categoryLower.includes('system')) {
              color = 'indigo'
            } else if (categoryLower.includes('contact')) {
              color = 'emerald'
            }
          }
          
          return {
            ...cat,
            icon: ICON_MAP[iconName] || DocumentTextIcon,
            color: color
          }
        })
        setCategories(categoriesWithIcons)
        console.log('Categories with icons:', categoriesWithIcons)

        // Set first category as active if none selected
        if (!activeCategory && categoriesWithIcons.length > 0) {
          setActiveCategory(categoriesWithIcons[0].name)
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      showMessage('error', 'Failed to load categories')
    }
  }

  // Fetch all settings from database
  const fetchSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/settings?limit=1000')
      const result = await response.json()

      if (result.success && result.data) {
        setSettings(result.data)
        // Debug log: print settings to check categorystyle
        console.log('Fetched settings:', result.data)
        // Auto-organize after fetch
        organizeSettings(result.data)
      }
    } catch (error) {
      console.error('Error fetching settings:', error)
      showMessage('error', 'Failed to load settings')
    } finally {
      setLoading(false)
    }
  }

  // Enhanced organization function
  const organizeSettings = (settingsToOrganize: Setting[] = settings) => {
    const organized = [...settingsToOrganize].sort((a, b) => {
      // First sort by category priority
      const categoryA = categories.findIndex(c => c.name === a.category)
      const categoryB = categories.findIndex(c => c.name === b.category)
      
      if (categoryA !== categoryB) {
        // Known categories first, then alphabetical for custom categories
        if (categoryA === -1 && categoryB === -1) {
          return a.category.localeCompare(b.category)
        }
        if (categoryA === -1) return 1
        if (categoryB === -1) return -1
        return categoryA - categoryB
      }
      
      // Within category, sort by key alphabetically
      return a.key.localeCompare(b.key)
    })
    
    setSettings(organized)
  }

  // Enhanced message system
  const showMessage = (type: 'success' | 'error' | 'warning', text: string) => {
    setMessage({ type, text })
    setTimeout(() => setMessage(null), 5000)
  }

  // Save all pending changes
  const saveAllChanges = async () => {
    try {
      setSaving(true)
      showMessage('success', 'All changes saved successfully')
      setHasUnsavedChanges(false)
      await fetchSettings()
    } catch (error) {
      console.error('Error saving changes:', error)
      showMessage('error', 'Failed to save changes')
    } finally {
      setSaving(false)
    }
  }

  // Save setting with enhanced feedback
  const saveSetting = async () => {
    try {
      setSaving(true)

      // Validate required fields
      if (!formData.key.trim()) {
        showMessage('error', 'Setting key is required')
        return
      }

      // Use newCategoryLabel if category is __NEW__
      let categoryToSave = formData.category
      if (formData.category === '__NEW__') {
        if (!formData.newCategoryLabel || !formData.newCategoryLabel.trim()) {
          showMessage('error', 'Please enter a new category name')
          setSaving(false)
          return
        }
        categoryToSave = formData.newCategoryLabel.trim()
      }

      // Prepare data for saving
      let categorystyle = undefined
      if (categoryToSave && categoryToSave !== '__NEW__') {
        const cat = categories.find(c => c.name === categoryToSave)
        if (cat && cat.categorystyle) categorystyle = cat.categorystyle
      }
      const dataToSave = {
        ...formData,
        category: categoryToSave,
        fieldType: formData.fieldType,
        options: formData.options,
        isactive: formData.isactive,
        ispublic: formData.ispublic,
        categorystyle
      }

      // Save to backend
      const url = isEditMode ? `/api/admin/settings/${editingSetting?.id}` : '/api/admin/settings'
      const response = await fetch(url, {
        method: isEditMode ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(dataToSave)
      })

      let result
      let responseText = ''
      try {
        responseText = await response.text()
        console.log('Response status:', response.status)
        console.log('Response text:', responseText)
        result = responseText ? JSON.parse(responseText) : { success: false, error: 'Empty response from server' }
      } catch (parseError) {
        console.error('JSON parse error:', parseError)
        result = { success: false, error: 'Invalid response from server' }
      }
      
      if (response.status === 401 || response.status === 403) {
        showMessage('error', 'You are not authorized to perform this action. Please sign in as an admin.')
        setSaving(false)
        return
      }

      if (result.success) {
        await fetchSettings()
        setIsModalOpen(false)
        showMessage('success', `Setting ${isEditMode ? 'updated' : 'created'} successfully`)
        // Refresh the page to reflect updates in categories and settings
        window.location.reload()
      } else {
        showMessage('error', result.error || 'Failed to save setting')
      }
    } catch (error) {
      console.error('Error saving setting:', error)
      showMessage('error', 'Failed to save setting')
    } finally {
      setSaving(false)
    }
  }

  // Delete setting with confirmation
  const deleteSetting = async (id: string, key: string) => {
    if (!confirm(`Are you sure you want to delete the setting "${key}"?`)) return
    
    try {
      const response = await fetch(`/api/admin/settings/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        showMessage('success', 'Setting deleted successfully')
        await fetchSettings()
      } else {
        throw new Error('Failed to delete setting')
      }
    } catch (error) {
      console.error('Error deleting setting:', error)
      showMessage('error', 'Failed to delete setting')
    }
  }

  // Update setting value inline with optimistic updates
  const updateSettingValue = async (setting: Setting, newValue: string) => {
    try {
      // Optimistic update
      setSettings(prev => prev.map(s =>
        s.id === setting.id ? { ...s, value: newValue } : s
      ))
      setHasUnsavedChanges(true)

      const response = await fetch(`/api/admin/settings/${setting.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...setting,
          value: newValue,
          fieldType: setting.fieldtype,
        }),
      })

      if (!response.ok) {
        // Revert optimistic update on error
        setSettings(prev => prev.map(s =>
          s.id === setting.id ? { ...s, value: setting.value } : s
        ))
        setHasUnsavedChanges(false)
        throw new Error('Failed to update setting')
      }

      setHasUnsavedChanges(false)
      showMessage('success', 'Setting updated successfully')
    } catch (error) {
      console.error('Error updating setting:', error)
      showMessage('error', 'Failed to update setting')
    }
  }

  // Enhanced field rendering with optimal widths and professional styling
  const renderDynamicField = (setting: Setting) => {
    return <FieldEditor setting={setting} updateSettingValue={updateSettingValue} forceLongInput={true} />
  }


  // Get category configuration from database-driven categories
  const getCategoryConfig = (categoryName: string) => {
    const foundCategory = categories.find(c => c.name === categoryName)
    if (foundCategory) {
      return foundCategory
    }
    
    // For categories not found in the database, create a default config
    // Try to infer icon based on category name
    let inferredIcon = DocumentTextIcon
    const categoryLower = categoryName.toLowerCase()
    
    // Comprehensive icon inference with many more options
    // General/System categories
    if (categoryLower.includes('general') || categoryLower.includes('basic') || categoryLower.includes('main')) {
      inferredIcon = CogIcon
    } else if (categoryLower.includes('system') || categoryLower.includes('server') || categoryLower.includes('infrastructure')) {
      inferredIcon = ServerIcon
    } else if (categoryLower.includes('config') || categoryLower.includes('setup') || categoryLower.includes('preferences')) {
      inferredIcon = Cog6ToothIcon
    }
    
    // User/Auth categories
    else if (categoryLower.includes('user') || categoryLower.includes('auth') || categoryLower.includes('login') || categoryLower.includes('account')) {
      inferredIcon = UserIcon
    } else if (categoryLower.includes('profile') || categoryLower.includes('avatar') || categoryLower.includes('picture')) {
      inferredIcon = UserCircleIcon
    } else if (categoryLower.includes('role') || categoryLower.includes('permission') || categoryLower.includes('access')) {
      inferredIcon = KeyIcon
    }
    
    // Communication categories
    else if (categoryLower.includes('email') || categoryLower.includes('mail') || categoryLower.includes('smtp')) {
      inferredIcon = EnvelopeIcon
    } else if (categoryLower.includes('chat') || categoryLower.includes('message') || categoryLower.includes('conversation')) {
      inferredIcon = ChatBubbleLeftRightIcon
    } else if (categoryLower.includes('notification') || categoryLower.includes('alert') || categoryLower.includes('reminder')) {
      inferredIcon = BellIcon
    } else if (categoryLower.includes('sms') || categoryLower.includes('text') || categoryLower.includes('mobile')) {
      inferredIcon = DevicePhoneMobileIcon
    }
    
    // Social/Network categories
    else if (categoryLower.includes('social') || categoryLower.includes('network') || categoryLower.includes('community')) {
      inferredIcon = GlobeAltIcon
    } else if (categoryLower.includes('facebook') || categoryLower.includes('twitter') || categoryLower.includes('instagram')) {
      inferredIcon = ShareIcon
    } else if (categoryLower.includes('share') || categoryLower.includes('like') || categoryLower.includes('follow')) {
      inferredIcon = HeartIcon
    }
    
    // Payment/Financial categories
    else if (categoryLower.includes('payment') || categoryLower.includes('billing') || categoryLower.includes('invoice')) {
      inferredIcon = CreditCardIcon
    } else if (categoryLower.includes('stripe') || categoryLower.includes('paypal') || categoryLower.includes('gateway')) {
      inferredIcon = BanknotesIcon
    } else if (categoryLower.includes('subscription') || categoryLower.includes('plan') || categoryLower.includes('pricing')) {
      inferredIcon = CurrencyDollarIcon
    } else if (categoryLower.includes('tax') || categoryLower.includes('vat') || categoryLower.includes('gst')) {
      inferredIcon = ReceiptPercentIcon
    }
    
    // Security/Privacy categories
    else if (categoryLower.includes('security') || categoryLower.includes('privacy') || categoryLower.includes('protection')) {
      inferredIcon = ShieldCheckIcon
    } else if (categoryLower.includes('encryption') || categoryLower.includes('hash') || categoryLower.includes('password')) {
      inferredIcon = LockClosedIcon
    } else if (categoryLower.includes('firewall') || categoryLower.includes('antivirus') || categoryLower.includes('malware')) {
      inferredIcon = ShieldExclamationIcon
    } else if (categoryLower.includes('backup') || categoryLower.includes('restore') || categoryLower.includes('recovery')) {
      inferredIcon = ArrowPathIcon
    }
    
    // Appearance/UI categories
    else if (categoryLower.includes('appearance') || categoryLower.includes('theme') || categoryLower.includes('style')) {
      inferredIcon = PaintBrushIcon
    } else if (categoryLower.includes('color') || categoryLower.includes('palette') || categoryLower.includes('design')) {
      inferredIcon = SwatchIcon
    } else if (categoryLower.includes('font') || categoryLower.includes('typography') || categoryLower.includes('text')) {
      inferredIcon = DocumentTextIcon
    } else if (categoryLower.includes('layout') || categoryLower.includes('template') || categoryLower.includes('structure')) {
      inferredIcon = Squares2X2Icon
    }
    
    // Contact/Location categories
    else if (categoryLower.includes('contact') || categoryLower.includes('phone') || categoryLower.includes('call')) {
      inferredIcon = PhoneIcon
    } else if (categoryLower.includes('address') || categoryLower.includes('location') || categoryLower.includes('map')) {
      inferredIcon = MapPinIcon
    } else if (categoryLower.includes('timezone') || categoryLower.includes('time') || categoryLower.includes('schedule')) {
      inferredIcon = ClockIcon
    } else if (categoryLower.includes('country') || categoryLower.includes('region') || categoryLower.includes('locale')) {
      inferredIcon = FlagIcon
    }
    
    // Analytics/Data categories
    else if (categoryLower.includes('analytics') || categoryLower.includes('chart') || categoryLower.includes('statistics')) {
      inferredIcon = ChartBarIcon
    } else if (categoryLower.includes('report') || categoryLower.includes('dashboard') || categoryLower.includes('metrics')) {
      inferredIcon = ChartBarSquareIcon
    } else if (categoryLower.includes('data') || categoryLower.includes('database') || categoryLower.includes('storage')) {
      inferredIcon = CircleStackIcon
    } else if (categoryLower.includes('export') || categoryLower.includes('import') || categoryLower.includes('sync')) {
      inferredIcon = ArrowDownTrayIcon
    }
    
    // Cloud/Storage categories
    else if (categoryLower.includes('cloud') || categoryLower.includes('aws') || categoryLower.includes('azure')) {
      inferredIcon = CloudIcon
    } else if (categoryLower.includes('file') || categoryLower.includes('upload') || categoryLower.includes('media')) {
      inferredIcon = DocumentIcon
    } else if (categoryLower.includes('image') || categoryLower.includes('photo') || categoryLower.includes('picture')) {
      inferredIcon = PhotoIcon
    } else if (categoryLower.includes('video') || categoryLower.includes('movie') || categoryLower.includes('stream')) {
      inferredIcon = VideoCameraIcon
    }
    
    // Integration/API categories
    else if (categoryLower.includes('api') || categoryLower.includes('endpoint') || categoryLower.includes('webhook')) {
      inferredIcon = CodeBracketIcon
    } else if (categoryLower.includes('integration') || categoryLower.includes('connect') || categoryLower.includes('link')) {
      inferredIcon = LinkIcon
    } else if (categoryLower.includes('oauth') || categoryLower.includes('sso') || categoryLower.includes('federation')) {
      inferredIcon = KeyIcon
    } else if (categoryLower.includes('webhook') || categoryLower.includes('callback') || categoryLower.includes('hook')) {
      inferredIcon = ArrowPathRoundedSquareIcon
    }
    
    // Performance/Technical categories
    else if (categoryLower.includes('performance') || categoryLower.includes('speed') || categoryLower.includes('optimization')) {
      inferredIcon = BoltIcon
    } else if (categoryLower.includes('cache') || categoryLower.includes('memory') || categoryLower.includes('redis')) {
      inferredIcon = CpuChipIcon
    } else if (categoryLower.includes('cdn') || categoryLower.includes('delivery') || categoryLower.includes('distribution')) {
      inferredIcon = GlobeAltIcon
    } else if (categoryLower.includes('ssl') || categoryLower.includes('certificate') || categoryLower.includes('https')) {
      inferredIcon = LockClosedIcon
    }
    
    // Business/Marketing categories
    else if (categoryLower.includes('business') || categoryLower.includes('company') || categoryLower.includes('organization')) {
      inferredIcon = BuildingOfficeIcon
    } else if (categoryLower.includes('marketing') || categoryLower.includes('campaign') || categoryLower.includes('promotion')) {
      inferredIcon = MegaphoneIcon
    } else if (categoryLower.includes('seo') || categoryLower.includes('search') || categoryLower.includes('google')) {
      inferredIcon = MagnifyingGlassIcon
    } else if (categoryLower.includes('brand') || categoryLower.includes('logo') || categoryLower.includes('identity')) {
      inferredIcon = StarIcon
    }
    
    // Support/Help categories
    else if (categoryLower.includes('support') || categoryLower.includes('help') || categoryLower.includes('assistance')) {
      inferredIcon = QuestionMarkCircleIcon
    } else if (categoryLower.includes('faq') || categoryLower.includes('knowledge') || categoryLower.includes('guide')) {
      inferredIcon = BookOpenIcon
    } else if (categoryLower.includes('ticket') || categoryLower.includes('issue') || categoryLower.includes('bug')) {
      inferredIcon = ExclamationTriangleIcon
    } else if (categoryLower.includes('feedback') || categoryLower.includes('review') || categoryLower.includes('rating')) {
      inferredIcon = ChatBubbleOvalLeftIcon
    }
    
    // Legal/Compliance categories
    else if (categoryLower.includes('legal') || categoryLower.includes('terms') || categoryLower.includes('privacy')) {
      inferredIcon = DocumentTextIcon
    } else if (categoryLower.includes('gdpr') || categoryLower.includes('compliance') || categoryLower.includes('regulation')) {
      inferredIcon = ShieldCheckIcon
    } else if (categoryLower.includes('cookie') || categoryLower.includes('consent') || categoryLower.includes('permission')) {
      inferredIcon = CheckCircleIcon
    }
    
    // Development/Technical categories
    else if (categoryLower.includes('development') || categoryLower.includes('dev') || categoryLower.includes('code')) {
      inferredIcon = CodeBracketIcon
    } else if (categoryLower.includes('testing') || categoryLower.includes('test') || categoryLower.includes('qa')) {
      inferredIcon = BeakerIcon
    } else if (categoryLower.includes('deployment') || categoryLower.includes('release') || categoryLower.includes('version')) {
      inferredIcon = RocketLaunchIcon
    } else if (categoryLower.includes('monitoring') || categoryLower.includes('log') || categoryLower.includes('error')) {
      inferredIcon = ExclamationTriangleIcon
    }
    
    // Content/Media categories
    else if (categoryLower.includes('content') || categoryLower.includes('article') || categoryLower.includes('blog')) {
      inferredIcon = DocumentTextIcon
    } else if (categoryLower.includes('news') || categoryLower.includes('announcement') || categoryLower.includes('update')) {
      inferredIcon = NewspaperIcon
    } else if (categoryLower.includes('gallery') || categoryLower.includes('album') || categoryLower.includes('collection')) {
      inferredIcon = PhotoIcon
    } else if (categoryLower.includes('download') || categoryLower.includes('attachment') || categoryLower.includes('file')) {
      inferredIcon = ArrowDownTrayIcon
    }
    
    // Language/Localization categories
    else if (categoryLower.includes('language') || categoryLower.includes('locale') || categoryLower.includes('translation')) {
      inferredIcon = LanguageIcon
    } else if (categoryLower.includes('currency') || categoryLower.includes('money') || categoryLower.includes('exchange')) {
      inferredIcon = CurrencyDollarIcon
    } else if (categoryLower.includes('date') || categoryLower.includes('calendar') || categoryLower.includes('time')) {
      inferredIcon = CalendarIcon
    }
    
    // Mobile/App categories
    else if (categoryLower.includes('mobile') || categoryLower.includes('app') || categoryLower.includes('ios')) {
      inferredIcon = DevicePhoneMobileIcon
    } else if (categoryLower.includes('android') || categoryLower.includes('google') || categoryLower.includes('play')) {
      inferredIcon = DeviceTabletIcon
    } else if (categoryLower.includes('push') || categoryLower.includes('notification') || categoryLower.includes('alert')) {
      inferredIcon = BellIcon
    }
    
    // E-commerce/Shop categories
    else if (categoryLower.includes('shop') || categoryLower.includes('store') || categoryLower.includes('product')) {
      inferredIcon = ShoppingBagIcon
    } else if (categoryLower.includes('cart') || categoryLower.includes('checkout') || categoryLower.includes('order')) {
      inferredIcon = ShoppingCartIcon
    } else if (categoryLower.includes('inventory') || categoryLower.includes('stock') || categoryLower.includes('warehouse')) {
      inferredIcon = ArchiveBoxIcon
    }
    
    // Comprehensive color inference with many more options
    let inferredColor = 'gray'
    
    // Blue themes - Professional, Trust, Technology
    if (categoryLower.includes('general') || categoryLower.includes('main') || categoryLower.includes('basic')) {
      inferredColor = 'blue'
    } else if (categoryLower.includes('system') || categoryLower.includes('config') || categoryLower.includes('setup')) {
      inferredColor = 'indigo'
    } else if (categoryLower.includes('api') || categoryLower.includes('integration') || categoryLower.includes('webhook')) {
      inferredColor = 'blue'
    } else if (categoryLower.includes('development') || categoryLower.includes('code') || categoryLower.includes('technical')) {
      inferredColor = 'blue'
    }
    
    // Green themes - Success, Money, Growth
    else if (categoryLower.includes('payment') || categoryLower.includes('billing') || categoryLower.includes('subscription')) {
      inferredColor = 'green'
    } else if (categoryLower.includes('success') || categoryLower.includes('completed') || categoryLower.includes('verified')) {
      inferredColor = 'emerald'
    } else if (categoryLower.includes('business') || categoryLower.includes('company') || categoryLower.includes('organization')) {
      inferredColor = 'green'
    } else if (categoryLower.includes('growth') || categoryLower.includes('progress') || categoryLower.includes('improvement')) {
      inferredColor = 'emerald'
    }
    
    // Red themes - Security, Danger, Errors
    else if (categoryLower.includes('security') || categoryLower.includes('privacy') || categoryLower.includes('protection')) {
      inferredColor = 'red'
    } else if (categoryLower.includes('error') || categoryLower.includes('bug') || categoryLower.includes('issue')) {
      inferredColor = 'red'
    } else if (categoryLower.includes('warning') || categoryLower.includes('alert') || categoryLower.includes('danger')) {
      inferredColor = 'red'
    } else if (categoryLower.includes('delete') || categoryLower.includes('remove') || categoryLower.includes('trash')) {
      inferredColor = 'red'
    }
    
    // Yellow themes - Warning, Attention, Performance
    else if (categoryLower.includes('notification') || categoryLower.includes('alert') || categoryLower.includes('reminder')) {
      inferredColor = 'yellow'
    } else if (categoryLower.includes('performance') || categoryLower.includes('speed') || categoryLower.includes('optimization')) {
      inferredColor = 'yellow'
    } else if (categoryLower.includes('warning') || categoryLower.includes('caution') || categoryLower.includes('attention')) {
      inferredColor = 'yellow'
    } else if (categoryLower.includes('maintenance') || categoryLower.includes('update') || categoryLower.includes('upgrade')) {
      inferredColor = 'yellow'
    }
    
    // Purple themes - Creative, Social, Premium
    else if (categoryLower.includes('social') || categoryLower.includes('network') || categoryLower.includes('community')) {
      inferredColor = 'purple'
    } else if (categoryLower.includes('appearance') || categoryLower.includes('theme') || categoryLower.includes('design')) {
      inferredColor = 'purple'
    } else if (categoryLower.includes('creative') || categoryLower.includes('art') || categoryLower.includes('media')) {
      inferredColor = 'purple'
    } else if (categoryLower.includes('premium') || categoryLower.includes('vip') || categoryLower.includes('exclusive')) {
      inferredColor = 'purple'
    }
    
    // Pink themes - Marketing, Brand, Fashion
    else if (categoryLower.includes('marketing') || categoryLower.includes('campaign') || categoryLower.includes('promotion')) {
      inferredColor = 'pink'
    } else if (categoryLower.includes('brand') || categoryLower.includes('logo') || categoryLower.includes('identity')) {
      inferredColor = 'pink'
    } else if (categoryLower.includes('fashion') || categoryLower.includes('style') || categoryLower.includes('beauty')) {
      inferredColor = 'pink'
    } else if (categoryLower.includes('romance') || categoryLower.includes('love') || categoryLower.includes('heart')) {
      inferredColor = 'pink'
    }
    
    // Emerald themes - Nature, Health, Environment
    else if (categoryLower.includes('health') || categoryLower.includes('medical') || categoryLower.includes('wellness')) {
      inferredColor = 'emerald'
    } else if (categoryLower.includes('nature') || categoryLower.includes('environment') || categoryLower.includes('eco')) {
      inferredColor = 'emerald'
    } else if (categoryLower.includes('fitness') || categoryLower.includes('sport') || categoryLower.includes('exercise')) {
      inferredColor = 'emerald'
    } else if (categoryLower.includes('organic') || categoryLower.includes('natural') || categoryLower.includes('green')) {
      inferredColor = 'emerald'
    }
    
    // Indigo themes - Technology, Innovation, Future
    else if (categoryLower.includes('technology') || categoryLower.includes('innovation') || categoryLower.includes('future')) {
      inferredColor = 'indigo'
    } else if (categoryLower.includes('ai') || categoryLower.includes('machine') || categoryLower.includes('automation')) {
      inferredColor = 'indigo'
    } else if (categoryLower.includes('blockchain') || categoryLower.includes('crypto') || categoryLower.includes('web3')) {
      inferredColor = 'indigo'
    } else if (categoryLower.includes('cloud') || categoryLower.includes('aws') || categoryLower.includes('azure')) {
      inferredColor = 'indigo'
    }
    
    // Orange themes - Energy, Action, Creativity
    else if (categoryLower.includes('energy') || categoryLower.includes('power') || categoryLower.includes('electric')) {
      inferredColor = 'orange'
    } else if (categoryLower.includes('action') || categoryLower.includes('activity') || categoryLower.includes('movement')) {
      inferredColor = 'orange'
    } else if (categoryLower.includes('creative') || categoryLower.includes('art') || categoryLower.includes('design')) {
      inferredColor = 'orange'
    } else if (categoryLower.includes('fire') || categoryLower.includes('hot') || categoryLower.includes('burn')) {
      inferredColor = 'orange'
    }
    
    // Teal themes - Communication, Information, Knowledge
    else if (categoryLower.includes('communication') || categoryLower.includes('message') || categoryLower.includes('chat')) {
      inferredColor = 'teal'
    } else if (categoryLower.includes('information') || categoryLower.includes('data') || categoryLower.includes('knowledge')) {
      inferredColor = 'teal'
    } else if (categoryLower.includes('education') || categoryLower.includes('learning') || categoryLower.includes('training')) {
      inferredColor = 'teal'
    } else if (categoryLower.includes('research') || categoryLower.includes('study') || categoryLower.includes('analysis')) {
      inferredColor = 'teal'
    }
    
    // Cyan themes - Water, Cool, Fresh
    else if (categoryLower.includes('water') || categoryLower.includes('ocean') || categoryLower.includes('sea')) {
      inferredColor = 'cyan'
    } else if (categoryLower.includes('cool') || categoryLower.includes('fresh') || categoryLower.includes('clean')) {
      inferredColor = 'cyan'
    } else if (categoryLower.includes('ice') || categoryLower.includes('cold') || categoryLower.includes('winter')) {
      inferredColor = 'cyan'
    }
    
    // Rose themes - Love, Romance, Passion
    else if (categoryLower.includes('love') || categoryLower.includes('romance') || categoryLower.includes('passion')) {
      inferredColor = 'rose'
    } else if (categoryLower.includes('heart') || categoryLower.includes('emotion') || categoryLower.includes('feeling')) {
      inferredColor = 'rose'
    }
    
    // Amber themes - Warm, Autumn, Harvest
    else if (categoryLower.includes('warm') || categoryLower.includes('autumn') || categoryLower.includes('harvest')) {
      inferredColor = 'amber'
    } else if (categoryLower.includes('gold') || categoryLower.includes('premium') || categoryLower.includes('luxury')) {
      inferredColor = 'amber'
    }
    
    // Lime themes - Spring, Growth, Youth
    else if (categoryLower.includes('spring') || categoryLower.includes('growth') || categoryLower.includes('youth')) {
      inferredColor = 'lime'
    } else if (categoryLower.includes('fresh') || categoryLower.includes('new') || categoryLower.includes('start')) {
      inferredColor = 'lime'
    }
    
    // Sky themes - Air, Freedom, Open
    else if (categoryLower.includes('air') || categoryLower.includes('freedom') || categoryLower.includes('open')) {
      inferredColor = 'sky'
    } else if (categoryLower.includes('sky') || categoryLower.includes('heaven') || categoryLower.includes('spiritual')) {
      inferredColor = 'sky'
    }
    
    // Violet themes - Royal, Luxury, Mystery
    else if (categoryLower.includes('royal') || categoryLower.includes('luxury') || categoryLower.includes('mystery')) {
      inferredColor = 'violet'
    } else if (categoryLower.includes('magic') || categoryLower.includes('mystical') || categoryLower.includes('spiritual')) {
      inferredColor = 'violet'
    }
    
    // Fuchsia themes - Bold, Vibrant, Energetic
    else if (categoryLower.includes('bold') || categoryLower.includes('vibrant') || categoryLower.includes('energetic')) {
      inferredColor = 'fuchsia'
    } else if (categoryLower.includes('party') || categoryLower.includes('celebration') || categoryLower.includes('fun')) {
      inferredColor = 'fuchsia'
    }
    
    return {
      name: categoryName,
      label: categoryName.charAt(0) + categoryName.slice(1).toLowerCase(),
      icon: inferredIcon,
      color: inferredColor,
      description: 'Custom category settings',
      order: 999
    }
  }

  // Get color classes for category
  const getCategoryColorClasses = (color: string) => {
    const colorMap: Record<string, { bg: string; text: string; border: string; hover: string }> = {
      blue: { bg: 'bg-blue-50', text: 'text-blue-700', border: 'border-blue-200', hover: 'hover:bg-blue-100' },
      green: { bg: 'bg-green-50', text: 'text-green-700', border: 'border-green-200', hover: 'hover:bg-green-100' },
      yellow: { bg: 'bg-yellow-50', text: 'text-yellow-700', border: 'border-yellow-200', hover: 'hover:bg-yellow-100' },
      red: { bg: 'bg-red-50', text: 'text-red-700', border: 'border-red-200', hover: 'hover:bg-red-100' },
      purple: { bg: 'bg-purple-50', text: 'text-purple-700', border: 'border-purple-200', hover: 'hover:bg-purple-100' },
      indigo: { bg: 'bg-indigo-50', text: 'text-indigo-700', border: 'border-indigo-200', hover: 'hover:bg-indigo-100' },
      emerald: { bg: 'bg-emerald-50', text: 'text-emerald-700', border: 'border-emerald-200', hover: 'hover:bg-emerald-100' },
      gray: { bg: 'bg-gray-50', text: 'text-gray-700', border: 'border-gray-200', hover: 'hover:bg-gray-100' },
      pink: { bg: 'bg-pink-50', text: 'text-pink-700', border: 'border-pink-200', hover: 'hover:bg-pink-100' },
      orange: { bg: 'bg-orange-50', text: 'text-orange-700', border: 'border-orange-200', hover: 'hover:bg-orange-100' },
      teal: { bg: 'bg-teal-50', text: 'text-teal-700', border: 'border-teal-200', hover: 'hover:bg-teal-100' },
      cyan: { bg: 'bg-cyan-50', text: 'text-cyan-700', border: 'border-cyan-200', hover: 'hover:bg-cyan-100' },
      rose: { bg: 'bg-rose-50', text: 'text-rose-700', border: 'border-rose-200', hover: 'hover:bg-rose-100' },
      amber: { bg: 'bg-amber-50', text: 'text-amber-700', border: 'border-amber-200', hover: 'hover:bg-amber-100' },
      lime: { bg: 'bg-lime-50', text: 'text-lime-700', border: 'border-lime-200', hover: 'hover:bg-lime-100' },
      sky: { bg: 'bg-sky-50', text: 'text-sky-700', border: 'border-sky-200', hover: 'hover:bg-sky-100' },
      violet: { bg: 'bg-violet-50', text: 'text-violet-700', border: 'border-violet-200', hover: 'hover:bg-violet-100' },
      fuchsia: { bg: 'bg-fuchsia-50', text: 'text-fuchsia-700', border: 'border-fuchsia-200', hover: 'hover:bg-fuchsia-100' },
    }
    return colorMap[color] || colorMap.gray
  }

  // Filter and search settings
  const getFilteredSettings = () => {
    let filtered = settings.filter(s => s.category === activeCategory)

    // Hide JSON field types (used for category info)
    filtered = filtered.filter(s => s.fieldtype !== 'json')

    if (!showInactive) {
      filtered = filtered.filter(s => s.isactive)
    }

    if (searchTerm) {
      filtered = filtered.filter(s =>
        s.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
        s.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return filtered
  }

  // Modal functions
  const openModal = (category?: string) => {
    setFormData({
      key: '',
      value: '',
      description: '',
      category: category || activeCategory,
      fieldType: 'text',
      options: '',
      isactive: true,
      ispublic: true,
      newCategoryLabel: '',
    })
    setIsEditMode(false)
    setEditingSetting(null)
    setIsModalOpen(true)
  }

  const openEditModal = (setting: Setting) => {
    setFormData({
      key: setting.key,
      value: setting.value,
      description: setting.description || '',
      category: setting.category,
      fieldType: setting.fieldtype,
      options: setting.options || '',
      isactive: setting.isactive,
      ispublic: setting.ispublic,
      newCategoryLabel: '',
    })
    setIsEditMode(true)
    setEditingSetting(setting)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setIsEditMode(false)
    setEditingSetting(null)
  }

  // Load categories and settings when session is ready
  useEffect(() => {
    if (status === 'authenticated' && session) {
      const loadData = async () => {
        await fetchCategories()
        await fetchSettings()
      }
      loadData()
    }
  }, [status, session])

  // Auto-hide messages
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000)
      return () => clearTimeout(timer)
    }
  }, [message])

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading settings...</p>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 font-medium">Please sign in to access admin settings.</p>
        </div>
      </div>
    )
  }

  const filteredSettings = getFilteredSettings()
  const activeCategoryConfig = getCategoryConfig(activeCategory)
  const categoryColors = getCategoryColorClasses(activeCategoryConfig.color)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Enhanced Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <CogIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Settings Management</h1>
                  <p className="text-sm text-gray-500">Configure your application settings</p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={() => openModal()}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Setting
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Message Display */}
      {message && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <div className={`rounded-lg p-4 ${
            message.type === 'success' ? 'bg-green-50 border border-green-200' :
            message.type === 'warning' ? 'bg-yellow-50 border border-yellow-200' :
            'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                {message.type === 'success' ? (
                  <CheckIcon className="h-5 w-5 text-green-400" />
                ) : message.type === 'warning' ? (
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                ) : (
                  <XMarkIcon className="h-5 w-5 text-red-400" />
                )}
              </div>
              <div className="ml-3">
                <p className={`text-sm font-medium ${
                  message.type === 'success' ? 'text-green-800' :
                  message.type === 'warning' ? 'text-yellow-800' :
                  'text-red-800'
                }`}>
                  {message.text}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Compact Category Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Categories</h3>
                  <p className="text-sm text-gray-500">Organize settings</p>
                </div>
              </div>

              <nav className="p-2">
                {[...categories].sort((a, b) => a.label.localeCompare(b.label)).map((category) => {
                  const Icon = category.icon
                  const colors = getCategoryColorClasses(category.color)
                  const isActive = activeCategory === category.name
                  const settingsCount = settings.filter(s => s.category === category.name).length
                  const isHovered = hoveredCategory === category.name

                  return (
                    <div
                      key={category.name}
                      className="relative group"
                      onMouseEnter={() => setHoveredCategory(category.name)}
                      onMouseLeave={() => setHoveredCategory(null)}
                    >
                      <button
                        onClick={() => setActiveCategory(category.name)}
                        className={`${
                          isActive
                            ? `${colors.bg} ${colors.text} ${colors.border} border-l-4`
                            : 'text-gray-600 hover:bg-gray-50 border-l-4 border-transparent hover:border-gray-200'
                        } w-full flex items-center justify-between px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg mx-1 my-0.5 ${colors.hover}`}
                      >
                        <div className="flex items-center min-w-0">
                          <Icon className="h-4 w-4 mr-2 flex-shrink-0" />
                          <span className="truncate">{category.label}</span>
                        </div>
                        <span className={`${
                          isActive ? colors.text : 'text-gray-400'
                        } inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-white shadow-sm`}>
                          {settingsCount}
                        </span>
                      </button>
                    </div>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* Enhanced Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Category Header */}
              <div className={`${categoryColors.bg} ${categoryColors.border} border-b px-6 py-4`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 ${categoryColors.text} bg-white rounded-lg shadow-sm`}>
                      <activeCategoryConfig.icon className="h-6 w-6" />
                    </div>
                    <div>
                      <h2 className={`text-xl font-bold ${categoryColors.text}`}>
                        {activeCategoryConfig.label}
                      </h2>
                      <p className={`text-sm ${categoryColors.text} opacity-75`}>
                        {activeCategoryConfig.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${categoryColors.text} bg-white shadow-sm`}>
                      {filteredSettings.length} settings
                    </span>
                  </div>
                </div>
              </div>

              {/* Enhanced Controls */}
              <div className="p-6 border-b border-gray-200 bg-gray-50">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                  <div className="flex items-center space-x-3">
                    {/* Search */}
                    <div className="relative">
                      <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search settings..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm w-64"
                      />
                    </div>

                    {/* Show Inactive Toggle */}
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={showInactive}
                        onChange={(e) => setShowInactive(e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Show inactive</span>
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* View Mode Toggle */}
                    <div className="flex items-center bg-white border border-gray-300 rounded-lg p-1">
                      <button
                        onClick={() => setViewMode('grid')}
                        className={`p-1.5 rounded ${
                          viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
                        } transition-colors`}
                      >
                        <Squares2X2Icon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => setViewMode('list')}
                        className={`p-1.5 rounded ${
                          viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
                        } transition-colors`}
                      >
                        <ListBulletIcon className="h-4 w-4" />
                      </button>
                    </div>



                    {/* Add Setting Button */}
                    <button
                      onClick={() => openModal(activeCategory)}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                    >
                      <PlusIcon className="h-4 w-4 mr-1" />
                      Add
                    </button>
                  </div>
                </div>
              </div>

              {/* Compact Settings Content */}
              <div className="p-4">
                {filteredSettings.length === 0 ? (
                  <div className="text-center py-8">
                    <div className={`mx-auto h-12 w-12 ${categoryColors.bg} rounded-full flex items-center justify-center`}>
                      <activeCategoryConfig.icon className={`h-6 w-6 ${categoryColors.text}`} />
                    </div>
                    <h3 className="mt-3 text-base font-medium text-gray-900">
                      {searchTerm ? 'No matching settings' : 'No settings found'}
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {searchTerm
                        ? `No settings match "${searchTerm}" in this category.`
                        : `Create your first setting for ${activeCategoryConfig.label}.`
                      }
                    </p>
                    {!searchTerm && (
                      <div className="mt-4">
                        <button
                          onClick={() => openModal(activeCategory)}
                          className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                        >
                          <PlusIcon className="h-4 w-4 mr-2" />
                          Create First Setting
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className={`${
                    viewMode === 'grid'
                      ? 'grid grid-cols-1 md:grid-cols-2 gap-2'
                      : 'space-y-1'
                  }`}>
                    {filteredSettings.map((setting) => {
                      // Use style from setting.categorystyle if present, otherwise from category
                      let style = null;
                      try {
                        style = setting.categorystyle ? (typeof setting.categorystyle === 'string' ? JSON.parse(setting.categorystyle) : setting.categorystyle) : null;
                      } catch { style = null; }
                      const categoryObj = categories.find(c => c.name === setting.category);
                      const styleSource = style || (categoryObj && categoryObj.categorystyle) || {};
                      const color = styleSource.color || (categoryObj ? categoryObj.color : 'gray');
                      const iconName = styleSource.icon || (categoryObj ? categoryObj.icon : 'DocumentTextIcon');
                      const label = styleSource.label || (categoryObj ? categoryObj.label : setting.category);
                      const description = styleSource.description || (categoryObj ? categoryObj.description : '');
                      const Icon = ICON_MAP[iconName] || DocumentTextIcon;
                      const colors = getCategoryColorClasses(color);
                      return (
                        <div
                          key={setting.id}
                          className={`${viewMode === 'grid' ? 'bg-gray-50 border border-gray-200 rounded-lg p-2 hover:shadow-md transition-all duration-200' : 'bg-white border border-gray-200 rounded-lg p-2 hover:shadow-sm transition-all duration-200'} ${!setting.isactive ? 'opacity-60' : ''}`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-1 mb-1">
                                <Icon className={`h-4 w-4 ${colors.text} flex-shrink-0`} />
                                <h4 className="text-sm font-bold text-gray-900 truncate">
                                  {setting.key.replace(/_/g, ' ')}
                                </h4>
                                {!setting.isactive && (
                                  <span className="inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                    Inactive
                                  </span>
                                )}
                                {!setting.ispublic && (
                                  <span className="inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-600">
                                    Private
                                  </span>
                                )}
                              </div>
                              <div className="relative group">
                                <FieldEditor setting={setting} updateSettingValue={updateSettingValue} forceLongInput={true} />
                              </div>
                            </div>
                            <div className="flex items-center space-x-0.5 ml-2">
                              <button
                                onClick={() => openEditModal(setting)}
                                className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-all duration-200"
                                title="Edit setting"
                              >
                                <PencilIcon className="h-3 w-3" />
                              </button>
                              <button
                                onClick={() => deleteSetting(setting.id, setting.key)}
                                className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200"
                                title="Delete setting"
                              >
                                <TrashIcon className="h-3 w-3" />
                              </button>
                              {setting.description && (
                                <div className="relative inline-block">
                                  <button
                                    onClick={() => handleShowDesc(setting.id)}
                                    className="p-1 text-gray-400 hover:text-amber-600 hover:bg-amber-50 rounded transition-all duration-200"
                                    title="Show description"
                                    type="button"
                                  >
                                    <ExclamationTriangleIcon className="h-3 w-3" />
                                  </button>
                                  {showDescId === setting.id && (
                                    <div
                                      className="absolute z-20 left-1/2 -translate-x-1/2 mt-2 w-64 bg-gray-900 text-white text-xs rounded px-3 py-2 shadow-lg"
                                      onClick={handleHideDesc}
                                      style={{ cursor: 'pointer' }}
                                    >
                                      {setting.description}
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 ${categoryColors.bg} rounded-lg`}>
                    <activeCategoryConfig.icon className={`h-6 w-6 ${categoryColors.text}`} />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {isEditMode ? 'Edit Setting' : 'Create New Setting'}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {isEditMode ? 'Update setting configuration' : `Add a new setting to ${activeCategoryConfig.label}`}
                    </p>
                  </div>
                </div>
                <button
                  onClick={closeModal}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <form onSubmit={(e) => { e.preventDefault(); saveSetting(); }} className="p-6 space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Setting Key *
                  </label>
                  <input
                    type="text"
                    value={formData.key}
                    onChange={(e) => setFormData({ ...formData, key: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    placeholder="e.g., site_name, max_upload_size"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Unique identifier for this setting (can be changed when editing)
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Category *
                  </label>
                  <CreatableSelect
                    value={
                      formData.category === '__NEW__'
                        ? { value: '__NEW__', label: 'New Category' }
                        : categories.find(cat => cat.name === formData.category)
                        ? { value: formData.category, label: categories.find(cat => cat.name === formData.category)?.label }
                        : null
                    }
                    onChange={option => {
                      if (option?.value === '__NEW__') {
                        setFormData({ ...formData, category: '__NEW__' })
                      } else {
                        setFormData({ ...formData, category: option?.value || '' })
                      }
                    }}
                    options={[
                      ...categories.map(cat => ({ value: cat.name, label: cat.label })),
                      { value: '__NEW__', label: 'New Category' }
                    ]}
                    classNamePrefix="react-select"
                    placeholder="Select or create category..."
                    isSearchable
                    required
                  />
                  {/* Only show the label input when New Category is selected */}
                  {formData.category === '__NEW__' && (
                    <>
                      <input
                        type="text"
                        className="mt-2 w-full border border-blue-400 rounded-lg shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                        value={formData.newCategoryLabel || ''}
                        onChange={e => setFormData({ ...formData, newCategoryLabel: e.target.value })}
                        placeholder="Enter new category label..."
                        required
                      />
                      {/* Enhanced Preview of inferred icon and color */}
                      {formData.newCategoryLabel && (
                        <div className="mt-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
                          <div className="space-y-3">
                            {/* Current Preview */}
                            <div className="flex items-center space-x-3">
                              <div className="text-sm font-medium text-gray-700">Live Preview:</div>
                              <div className="flex items-center space-x-2">
                                {(() => {
                                  const categoryLower = formData.newCategoryLabel.toLowerCase()
                                  
                                  // Use the same comprehensive inference logic as the main system
                                  let iconName = 'DocumentTextIcon'
                                  
                                  // Comprehensive icon inference with many more options
                                  // General/System categories
                                  if (categoryLower.includes('general') || categoryLower.includes('basic') || categoryLower.includes('main')) {
                                    iconName = 'CogIcon'
                                  } else if (categoryLower.includes('system') || categoryLower.includes('server') || categoryLower.includes('infrastructure')) {
                                    iconName = 'ServerIcon'
                                  } else if (categoryLower.includes('config') || categoryLower.includes('setup') || categoryLower.includes('preferences')) {
                                    iconName = 'Cog6ToothIcon'
                                  }
                                  
                                  // User/Auth categories
                                  else if (categoryLower.includes('user') || categoryLower.includes('auth') || categoryLower.includes('login') || categoryLower.includes('account')) {
                                    iconName = 'UserIcon'
                                  } else if (categoryLower.includes('profile') || categoryLower.includes('avatar') || categoryLower.includes('picture')) {
                                    iconName = 'UserCircleIcon'
                                  } else if (categoryLower.includes('role') || categoryLower.includes('permission') || categoryLower.includes('access')) {
                                    iconName = 'KeyIcon'
                                  }
                                  
                                  // Communication categories
                                  else if (categoryLower.includes('email') || categoryLower.includes('mail') || categoryLower.includes('smtp')) {
                                    iconName = 'EnvelopeIcon'
                                  } else if (categoryLower.includes('chat') || categoryLower.includes('message') || categoryLower.includes('conversation')) {
                                    iconName = 'ChatBubbleLeftRightIcon'
                                  } else if (categoryLower.includes('notification') || categoryLower.includes('alert') || categoryLower.includes('reminder')) {
                                    iconName = 'BellIcon'
                                  } else if (categoryLower.includes('sms') || categoryLower.includes('text') || categoryLower.includes('mobile')) {
                                    iconName = 'DevicePhoneMobileIcon'
                                  }
                                  
                                  // Social/Network categories
                                  else if (categoryLower.includes('social') || categoryLower.includes('network') || categoryLower.includes('community')) {
                                    iconName = 'GlobeAltIcon'
                                  } else if (categoryLower.includes('facebook') || categoryLower.includes('twitter') || categoryLower.includes('instagram')) {
                                    iconName = 'ShareIcon'
                                  } else if (categoryLower.includes('share') || categoryLower.includes('like') || categoryLower.includes('follow')) {
                                    iconName = 'HeartIcon'
                                  }
                                  
                                  // Payment/Financial categories
                                  else if (categoryLower.includes('payment') || categoryLower.includes('billing') || categoryLower.includes('invoice')) {
                                    iconName = 'CreditCardIcon'
                                  } else if (categoryLower.includes('stripe') || categoryLower.includes('paypal') || categoryLower.includes('gateway')) {
                                    iconName = 'BanknotesIcon'
                                  } else if (categoryLower.includes('subscription') || categoryLower.includes('plan') || categoryLower.includes('pricing')) {
                                    iconName = 'CurrencyDollarIcon'
                                  } else if (categoryLower.includes('tax') || categoryLower.includes('vat') || categoryLower.includes('gst')) {
                                    iconName = 'ReceiptPercentIcon'
                                  }
                                  
                                  // Security/Privacy categories
                                  else if (categoryLower.includes('security') || categoryLower.includes('privacy') || categoryLower.includes('protection')) {
                                    iconName = 'ShieldCheckIcon'
                                  } else if (categoryLower.includes('encryption') || categoryLower.includes('hash') || categoryLower.includes('password')) {
                                    iconName = 'LockClosedIcon'
                                  } else if (categoryLower.includes('firewall') || categoryLower.includes('antivirus') || categoryLower.includes('malware')) {
                                    iconName = 'ShieldExclamationIcon'
                                  } else if (categoryLower.includes('backup') || categoryLower.includes('restore') || categoryLower.includes('recovery')) {
                                    iconName = 'ArrowPathIcon'
                                  }
                                  
                                  // Appearance/UI categories
                                  else if (categoryLower.includes('appearance') || categoryLower.includes('theme') || categoryLower.includes('style')) {
                                    iconName = 'PaintBrushIcon'
                                  } else if (categoryLower.includes('color') || categoryLower.includes('palette') || categoryLower.includes('design')) {
                                    iconName = 'SwatchIcon'
                                  } else if (categoryLower.includes('font') || categoryLower.includes('typography') || categoryLower.includes('text')) {
                                    iconName = 'DocumentTextIcon'
                                  } else if (categoryLower.includes('layout') || categoryLower.includes('template') || categoryLower.includes('structure')) {
                                    iconName = 'Squares2X2Icon'
                                  }
                                  
                                  // Contact/Location categories
                                  else if (categoryLower.includes('contact') || categoryLower.includes('phone') || categoryLower.includes('call')) {
                                    iconName = 'PhoneIcon'
                                  } else if (categoryLower.includes('address') || categoryLower.includes('location') || categoryLower.includes('map')) {
                                    iconName = 'MapPinIcon'
                                  } else if (categoryLower.includes('timezone') || categoryLower.includes('time') || categoryLower.includes('schedule')) {
                                    iconName = 'ClockIcon'
                                  } else if (categoryLower.includes('country') || categoryLower.includes('region') || categoryLower.includes('locale')) {
                                    iconName = 'FlagIcon'
                                  }
                                  
                                  // Analytics/Data categories
                                  else if (categoryLower.includes('analytics') || categoryLower.includes('chart') || categoryLower.includes('statistics')) {
                                    iconName = 'ChartBarIcon'
                                  } else if (categoryLower.includes('report') || categoryLower.includes('dashboard') || categoryLower.includes('metrics')) {
                                    iconName = 'ChartBarSquareIcon'
                                  } else if (categoryLower.includes('data') || categoryLower.includes('database') || categoryLower.includes('storage')) {
                                    iconName = 'CircleStackIcon'
                                  } else if (categoryLower.includes('export') || categoryLower.includes('import') || categoryLower.includes('sync')) {
                                    iconName = 'ArrowDownTrayIcon'
                                  }
                                  
                                  // Cloud/Storage categories
                                  else if (categoryLower.includes('cloud') || categoryLower.includes('aws') || categoryLower.includes('azure')) {
                                    iconName = 'CloudIcon'
                                  } else if (categoryLower.includes('file') || categoryLower.includes('upload') || categoryLower.includes('media')) {
                                    iconName = 'DocumentIcon'
                                  } else if (categoryLower.includes('image') || categoryLower.includes('photo') || categoryLower.includes('picture')) {
                                    iconName = 'PhotoIcon'
                                  } else if (categoryLower.includes('video') || categoryLower.includes('movie') || categoryLower.includes('stream')) {
                                    iconName = 'VideoCameraIcon'
                                  }
                                  
                                  // Integration/API categories
                                  else if (categoryLower.includes('api') || categoryLower.includes('endpoint') || categoryLower.includes('webhook')) {
                                    iconName = 'CodeBracketIcon'
                                  } else if (categoryLower.includes('integration') || categoryLower.includes('connect') || categoryLower.includes('link')) {
                                    iconName = 'LinkIcon'
                                  } else if (categoryLower.includes('oauth') || categoryLower.includes('sso') || categoryLower.includes('federation')) {
                                    iconName = 'KeyIcon'
                                  } else if (categoryLower.includes('webhook') || categoryLower.includes('callback') || categoryLower.includes('hook')) {
                                    iconName = 'ArrowPathRoundedSquareIcon'
                                  }
                                  
                                  // Performance/Technical categories
                                  else if (categoryLower.includes('performance') || categoryLower.includes('speed') || categoryLower.includes('optimization')) {
                                    iconName = 'BoltIcon'
                                  } else if (categoryLower.includes('cache') || categoryLower.includes('memory') || categoryLower.includes('redis')) {
                                    iconName = 'CpuChipIcon'
                                  } else if (categoryLower.includes('cdn') || categoryLower.includes('delivery') || categoryLower.includes('distribution')) {
                                    iconName = 'GlobeAltIcon'
                                  } else if (categoryLower.includes('ssl') || categoryLower.includes('certificate') || categoryLower.includes('https')) {
                                    iconName = 'LockClosedIcon'
                                  }
                                  
                                  // Business/Marketing categories
                                  else if (categoryLower.includes('business') || categoryLower.includes('company') || categoryLower.includes('organization')) {
                                    iconName = 'BuildingOfficeIcon'
                                  } else if (categoryLower.includes('marketing') || categoryLower.includes('campaign') || categoryLower.includes('promotion')) {
                                    iconName = 'MegaphoneIcon'
                                  } else if (categoryLower.includes('seo') || categoryLower.includes('search') || categoryLower.includes('google')) {
                                    iconName = 'MagnifyingGlassIcon'
                                  } else if (categoryLower.includes('brand') || categoryLower.includes('logo') || categoryLower.includes('identity')) {
                                    iconName = 'StarIcon'
                                  }
                                  
                                  // Support/Help categories
                                  else if (categoryLower.includes('support') || categoryLower.includes('help') || categoryLower.includes('assistance')) {
                                    iconName = 'QuestionMarkCircleIcon'
                                  } else if (categoryLower.includes('faq') || categoryLower.includes('knowledge') || categoryLower.includes('guide')) {
                                    iconName = 'BookOpenIcon'
                                  } else if (categoryLower.includes('ticket') || categoryLower.includes('issue') || categoryLower.includes('bug')) {
                                    iconName = 'ExclamationTriangleIcon'
                                  } else if (categoryLower.includes('feedback') || categoryLower.includes('review') || categoryLower.includes('rating')) {
                                    iconName = 'ChatBubbleOvalLeftIcon'
                                  }
                                  
                                  // Legal/Compliance categories
                                  else if (categoryLower.includes('legal') || categoryLower.includes('terms') || categoryLower.includes('privacy')) {
                                    iconName = 'DocumentTextIcon'
                                  } else if (categoryLower.includes('gdpr') || categoryLower.includes('compliance') || categoryLower.includes('regulation')) {
                                    iconName = 'ShieldCheckIcon'
                                  } else if (categoryLower.includes('cookie') || categoryLower.includes('consent') || categoryLower.includes('permission')) {
                                    iconName = 'CheckCircleIcon'
                                  }
                                  
                                  // Development/Technical categories
                                  else if (categoryLower.includes('development') || categoryLower.includes('dev') || categoryLower.includes('code')) {
                                    iconName = 'CodeBracketIcon'
                                  } else if (categoryLower.includes('testing') || categoryLower.includes('test') || categoryLower.includes('qa')) {
                                    iconName = 'BeakerIcon'
                                  } else if (categoryLower.includes('deployment') || categoryLower.includes('release') || categoryLower.includes('version')) {
                                    iconName = 'RocketLaunchIcon'
                                  } else if (categoryLower.includes('monitoring') || categoryLower.includes('log') || categoryLower.includes('error')) {
                                    iconName = 'ExclamationTriangleIcon'
                                  }
                                  
                                  // Content/Media categories
                                  else if (categoryLower.includes('content') || categoryLower.includes('article') || categoryLower.includes('blog')) {
                                    iconName = 'DocumentTextIcon'
                                  } else if (categoryLower.includes('news') || categoryLower.includes('announcement') || categoryLower.includes('update')) {
                                    iconName = 'NewspaperIcon'
                                  } else if (categoryLower.includes('gallery') || categoryLower.includes('album') || categoryLower.includes('collection')) {
                                    iconName = 'PhotoIcon'
                                  } else if (categoryLower.includes('download') || categoryLower.includes('attachment') || categoryLower.includes('file')) {
                                    iconName = 'ArrowDownTrayIcon'
                                  }
                                  
                                  // Language/Localization categories
                                  else if (categoryLower.includes('language') || categoryLower.includes('locale') || categoryLower.includes('translation')) {
                                    iconName = 'LanguageIcon'
                                  } else if (categoryLower.includes('currency') || categoryLower.includes('money') || categoryLower.includes('exchange')) {
                                    iconName = 'CurrencyDollarIcon'
                                  } else if (categoryLower.includes('date') || categoryLower.includes('calendar') || categoryLower.includes('time')) {
                                    iconName = 'CalendarIcon'
                                  }
                                  
                                  // Mobile/App categories
                                  else if (categoryLower.includes('mobile') || categoryLower.includes('app') || categoryLower.includes('ios')) {
                                    iconName = 'DevicePhoneMobileIcon'
                                  } else if (categoryLower.includes('android') || categoryLower.includes('google') || categoryLower.includes('play')) {
                                    iconName = 'DeviceTabletIcon'
                                  } else if (categoryLower.includes('push') || categoryLower.includes('notification') || categoryLower.includes('alert')) {
                                    iconName = 'BellIcon'
                                  }
                                  
                                  // E-commerce/Shop categories
                                  else if (categoryLower.includes('shop') || categoryLower.includes('store') || categoryLower.includes('product')) {
                                    iconName = 'ShoppingBagIcon'
                                  } else if (categoryLower.includes('cart') || categoryLower.includes('checkout') || categoryLower.includes('order')) {
                                    iconName = 'ShoppingCartIcon'
                                  } else if (categoryLower.includes('inventory') || categoryLower.includes('stock') || categoryLower.includes('warehouse')) {
                                    iconName = 'ArchiveBoxIcon'
                                  }
                                  
                                  // Comprehensive color inference with many more options
                                  let color = 'gray'
                                  
                                  // Blue themes - Professional, Trust, Technology
                                  if (categoryLower.includes('general') || categoryLower.includes('main') || categoryLower.includes('basic')) {
                                    color = 'blue'
                                  } else if (categoryLower.includes('system') || categoryLower.includes('config') || categoryLower.includes('setup')) {
                                    color = 'indigo'
                                  } else if (categoryLower.includes('api') || categoryLower.includes('integration') || categoryLower.includes('webhook')) {
                                    color = 'blue'
                                  } else if (categoryLower.includes('development') || categoryLower.includes('code') || categoryLower.includes('technical')) {
                                    color = 'blue'
                                  }
                                  
                                  // Green themes - Success, Money, Growth
                                  else if (categoryLower.includes('payment') || categoryLower.includes('billing') || categoryLower.includes('subscription')) {
                                    color = 'green'
                                  } else if (categoryLower.includes('success') || categoryLower.includes('completed') || categoryLower.includes('verified')) {
                                    color = 'emerald'
                                  } else if (categoryLower.includes('business') || categoryLower.includes('company') || categoryLower.includes('organization')) {
                                    color = 'green'
                                  } else if (categoryLower.includes('growth') || categoryLower.includes('progress') || categoryLower.includes('improvement')) {
                                    color = 'emerald'
                                  }
                                  
                                  // Red themes - Security, Danger, Errors
                                  else if (categoryLower.includes('security') || categoryLower.includes('privacy') || categoryLower.includes('protection')) {
                                    color = 'red'
                                  } else if (categoryLower.includes('error') || categoryLower.includes('bug') || categoryLower.includes('issue')) {
                                    color = 'red'
                                  } else if (categoryLower.includes('warning') || categoryLower.includes('alert') || categoryLower.includes('danger')) {
                                    color = 'red'
                                  } else if (categoryLower.includes('delete') || categoryLower.includes('remove') || categoryLower.includes('trash')) {
                                    color = 'red'
                                  }
                                  
                                  // Yellow themes - Warning, Attention, Performance
                                  else if (categoryLower.includes('notification') || categoryLower.includes('alert') || categoryLower.includes('reminder')) {
                                    color = 'yellow'
                                  } else if (categoryLower.includes('performance') || categoryLower.includes('speed') || categoryLower.includes('optimization')) {
                                    color = 'yellow'
                                  } else if (categoryLower.includes('warning') || categoryLower.includes('caution') || categoryLower.includes('attention')) {
                                    color = 'yellow'
                                  } else if (categoryLower.includes('maintenance') || categoryLower.includes('update') || categoryLower.includes('upgrade')) {
                                    color = 'yellow'
                                  }
                                  
                                  // Purple themes - Creative, Social, Premium
                                  else if (categoryLower.includes('social') || categoryLower.includes('network') || categoryLower.includes('community')) {
                                    color = 'purple'
                                  } else if (categoryLower.includes('appearance') || categoryLower.includes('theme') || categoryLower.includes('design')) {
                                    color = 'purple'
                                  } else if (categoryLower.includes('creative') || categoryLower.includes('art') || categoryLower.includes('media')) {
                                    color = 'purple'
                                  } else if (categoryLower.includes('premium') || categoryLower.includes('vip') || categoryLower.includes('exclusive')) {
                                    color = 'purple'
                                  }
                                  
                                  // Pink themes - Marketing, Brand, Fashion
                                  else if (categoryLower.includes('marketing') || categoryLower.includes('campaign') || categoryLower.includes('promotion')) {
                                    color = 'pink'
                                  } else if (categoryLower.includes('brand') || categoryLower.includes('logo') || categoryLower.includes('identity')) {
                                    color = 'pink'
                                  } else if (categoryLower.includes('fashion') || categoryLower.includes('style') || categoryLower.includes('beauty')) {
                                    color = 'pink'
                                  } else if (categoryLower.includes('romance') || categoryLower.includes('love') || categoryLower.includes('heart')) {
                                    color = 'pink'
                                  }
                                  
                                  // Emerald themes - Nature, Health, Environment
                                  else if (categoryLower.includes('health') || categoryLower.includes('medical') || categoryLower.includes('wellness')) {
                                    color = 'emerald'
                                  } else if (categoryLower.includes('nature') || categoryLower.includes('environment') || categoryLower.includes('eco')) {
                                    color = 'emerald'
                                  } else if (categoryLower.includes('fitness') || categoryLower.includes('sport') || categoryLower.includes('exercise')) {
                                    color = 'emerald'
                                  } else if (categoryLower.includes('organic') || categoryLower.includes('natural') || categoryLower.includes('green')) {
                                    color = 'emerald'
                                  }
                                  
                                  // Indigo themes - Technology, Innovation, Future
                                  else if (categoryLower.includes('technology') || categoryLower.includes('innovation') || categoryLower.includes('future')) {
                                    color = 'indigo'
                                  } else if (categoryLower.includes('ai') || categoryLower.includes('machine') || categoryLower.includes('automation')) {
                                    color = 'indigo'
                                  } else if (categoryLower.includes('blockchain') || categoryLower.includes('crypto') || categoryLower.includes('web3')) {
                                    color = 'indigo'
                                  } else if (categoryLower.includes('cloud') || categoryLower.includes('aws') || categoryLower.includes('azure')) {
                                    color = 'indigo'
                                  }
                                  
                                  // Orange themes - Energy, Action, Creativity
                                  else if (categoryLower.includes('energy') || categoryLower.includes('power') || categoryLower.includes('electric')) {
                                    color = 'orange'
                                  } else if (categoryLower.includes('action') || categoryLower.includes('activity') || categoryLower.includes('movement')) {
                                    color = 'orange'
                                  } else if (categoryLower.includes('creative') || categoryLower.includes('art') || categoryLower.includes('design')) {
                                    color = 'orange'
                                  } else if (categoryLower.includes('fire') || categoryLower.includes('hot') || categoryLower.includes('burn')) {
                                    color = 'orange'
                                  }
                                  
                                  // Teal themes - Communication, Information, Knowledge
                                  else if (categoryLower.includes('communication') || categoryLower.includes('message') || categoryLower.includes('chat')) {
                                    color = 'teal'
                                  } else if (categoryLower.includes('information') || categoryLower.includes('data') || categoryLower.includes('knowledge')) {
                                    color = 'teal'
                                  } else if (categoryLower.includes('education') || categoryLower.includes('learning') || categoryLower.includes('training')) {
                                    color = 'teal'
                                  } else if (categoryLower.includes('research') || categoryLower.includes('study') || categoryLower.includes('analysis')) {
                                    color = 'teal'
                                  }
                                  
                                  // Cyan themes - Water, Cool, Fresh
                                  else if (categoryLower.includes('water') || categoryLower.includes('ocean') || categoryLower.includes('sea')) {
                                    color = 'cyan'
                                  } else if (categoryLower.includes('cool') || categoryLower.includes('fresh') || categoryLower.includes('clean')) {
                                    color = 'cyan'
                                  } else if (categoryLower.includes('ice') || categoryLower.includes('cold') || categoryLower.includes('winter')) {
                                    color = 'cyan'
                                  }
                                  
                                  // Rose themes - Love, Romance, Passion
                                  else if (categoryLower.includes('love') || categoryLower.includes('romance') || categoryLower.includes('passion')) {
                                    color = 'rose'
                                  } else if (categoryLower.includes('heart') || categoryLower.includes('emotion') || categoryLower.includes('feeling')) {
                                    color = 'rose'
                                  }
                                  
                                  // Amber themes - Warm, Autumn, Harvest
                                  else if (categoryLower.includes('warm') || categoryLower.includes('autumn') || categoryLower.includes('harvest')) {
                                    color = 'amber'
                                  } else if (categoryLower.includes('gold') || categoryLower.includes('premium') || categoryLower.includes('luxury')) {
                                    color = 'amber'
                                  }
                                  
                                  // Lime themes - Spring, Growth, Youth
                                  else if (categoryLower.includes('spring') || categoryLower.includes('growth') || categoryLower.includes('youth')) {
                                    color = 'lime'
                                  } else if (categoryLower.includes('fresh') || categoryLower.includes('new') || categoryLower.includes('start')) {
                                    color = 'lime'
                                  }
                                  
                                  // Sky themes - Air, Freedom, Open
                                  else if (categoryLower.includes('air') || categoryLower.includes('freedom') || categoryLower.includes('open')) {
                                    color = 'sky'
                                  } else if (categoryLower.includes('sky') || categoryLower.includes('heaven') || categoryLower.includes('spiritual')) {
                                    color = 'sky'
                                  }
                                  
                                  // Violet themes - Royal, Luxury, Mystery
                                  else if (categoryLower.includes('royal') || categoryLower.includes('luxury') || categoryLower.includes('mystery')) {
                                    color = 'violet'
                                  } else if (categoryLower.includes('magic') || categoryLower.includes('mystical') || categoryLower.includes('spiritual')) {
                                    color = 'violet'
                                  }
                                  
                                  // Fuchsia themes - Bold, Vibrant, Energetic
                                  else if (categoryLower.includes('bold') || categoryLower.includes('vibrant') || categoryLower.includes('energetic')) {
                                    color = 'fuchsia'
                                  } else if (categoryLower.includes('party') || categoryLower.includes('celebration') || categoryLower.includes('fun')) {
                                    color = 'fuchsia'
                                  }
                                  
                                  const Icon = ICON_MAP[iconName] || DocumentTextIcon
                                  const colors = getCategoryColorClasses(color)
                                  
                                  return (
                                    <>
                                      <div className={`p-2 ${colors.bg} rounded-lg`}>
                                        <Icon className={`h-4 w-4 ${colors.text}`} />
                                      </div>
                                      <span className="text-sm text-gray-700">
                                        {formData.newCategoryLabel} ({color} {iconName})
                                      </span>
                                    </>
                                  )
                                })()}
                              </div>
                            </div>
                            
                            {/* Available Options Preview */}
                            <div className="border-t border-gray-200 pt-3">
                              <div className="text-xs font-medium text-gray-600 mb-2">Available Options:</div>
                              <div className="grid grid-cols-2 gap-2 text-xs">
                                <div>
                                  <div className="font-medium text-gray-700 mb-1">Popular Icons:</div>
                                  <div className="space-y-1">
                                    {['CogIcon', 'UserIcon', 'BellIcon', 'ShieldCheckIcon', 'CreditCardIcon', 'GlobeAltIcon', 'ChartBarIcon', 'CloudIcon'].map(iconName => (
                                      <div key={iconName} className="flex items-center space-x-1">
                                        <div className="w-3 h-3 bg-gray-100 rounded flex items-center justify-center">
                                          {(() => {
                                            const Icon = ICON_MAP[iconName]
                                            return Icon ? <Icon className="w-2 h-2 text-gray-500" /> : null
                                          })()}
                                        </div>
                                        <span className="text-gray-600">{iconName.replace('Icon', '')}</span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                                <div>
                                  <div className="font-medium text-gray-700 mb-1">Color Themes:</div>
                                  <div className="space-y-1">
                                    {['blue', 'green', 'red', 'yellow', 'purple', 'pink', 'emerald', 'indigo'].map(color => (
                                      <div key={color} className="flex items-center space-x-1">
                                        <div className={`w-3 h-3 rounded ${getCategoryColorClasses(color).bg}`}></div>
                                        <span className="text-gray-600 capitalize">{color}</span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                              <div className="mt-2 text-xs text-gray-500">
                                💡 Type keywords like "payment", "security", "social", "marketing" to see automatic icon & color matching
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Field Type *
                  </label>
                  <select
                    value={formData.fieldType}
                    onChange={(e) => setFormData({ ...formData, fieldType: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    required
                  >
                    {FIELD_TYPES.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Initial Value
                  </label>
                  <input
                    type="text"
                    value={formData.value}
                    onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    placeholder="Enter initial value"
                  />
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-none"
                  placeholder="Describe what this setting controls and how it affects the application"
                />
              </div>

              {/* Options for fields that need choices */}
              {(['dropdown', 'radio', 'range', 'toggle'].includes(formData.fieldType)) && (
                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    {formData.fieldType === 'range'
                      ? 'Range Options (min,max,step)'
                      : formData.fieldType === 'toggle'
                      ? 'Toggle Labels (off,on)'
                      : 'Choice Options (comma-separated)'}
                  </label>
                  <input
                    type="text"
                    value={formData.options}
                    onChange={(e) => setFormData({ ...formData, options: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    placeholder={
                      formData.fieldType === 'range'
                        ? "0,100,1"
                        : formData.fieldType === 'toggle'
                        ? "Off, On"
                        : "Option 1, Option 2, Option 3"
                    }
                    required={['dropdown', 'radio', 'range'].includes(formData.fieldType)}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.fieldType === 'range'
                      ? 'Format: minimum,maximum,step (e.g., 0,100,1)'
                      : formData.fieldType === 'toggle'
                      ? 'Labels for off and on states (optional)'
                      : 'Separate each choice option with a comma. These will be the available selections.'}
                  </p>
                </div>
              )}

              {/* Status Options */}
              <div className="grid grid-cols-2 gap-6">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <label className="text-sm font-semibold text-gray-900">Active Status</label>
                    <p className="text-xs text-gray-500">Enable or disable this setting</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.isactive}
                      onChange={(e) => setFormData({ ...formData, isactive: e.target.checked })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <label className="text-sm font-semibold text-gray-900">Public Access</label>
                    <p className="text-xs text-gray-500">Allow public API access</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.ispublic}
                      onChange={(e) => setFormData({ ...formData, ispublic: e.target.checked })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={closeModal}
                  className="px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center"
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <CheckIcon className="h-4 w-4 mr-2" />
                      {isEditMode ? 'Update Setting' : 'Create Setting'}
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
