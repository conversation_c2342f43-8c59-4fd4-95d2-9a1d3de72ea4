'use client';

import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { ServicesGrid } from '@/components/services/services-grid';
import { useStaticContent } from '@/lib/hooks/use-static-content';
import { useEffect, useState } from 'react';



export default function ServicesPage() {
  const { getContent } = useStaticContent();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="relative py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100">
          <div className="container">
            {isClient ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center max-w-4xl mx-auto"
              >
                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl"
                >
                  {getContent('services', 'hero', 'title', 'Our')} <span className="gradient-text">{getContent('services', 'hero', 'title_highlight', 'Services')}</span>
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl"
                >
                  {getContent('services', 'hero', 'subtitle', 'Comprehensive software development services to help your business thrive in the digital world. From concept to deployment, we\'ve got you covered.')}
                </motion.p>
              </motion.div>
            ) : (
              <div className="text-center max-w-4xl mx-auto">
                <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl">
                  {getContent('services', 'hero', 'title', 'Our')} <span className="gradient-text">{getContent('services', 'hero', 'title_highlight', 'Services')}</span>
                </h1>
                <p className="mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl">
                  {getContent('services', 'hero', 'subtitle', 'Comprehensive software development services to help your business thrive in the digital world. From concept to deployment, we\'ve got you covered.')}
                </p>
              </div>
            )}
          </div>
        </section>

        {/* Services Grid - Database Integrated */}
        <ServicesGrid
          variant="detailed"
          showSearch={true}
          showFilters={true}
          showCTA={true}
          title=""
          subtitle=""
        />

        {/* Process Section */}
        <section className="py-24 bg-gray-50">
          <div className="container">
            {isClient ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center mb-16"
              >
                <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                  {getContent('services', 'process', 'title', 'Our')} <span className="gradient-text">{getContent('services', 'process', 'title_highlight', 'Process')}</span>
                </h2>
                <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
                  {getContent('services', 'process', 'subtitle', 'A proven methodology that delivers results')}
                </p>
              </motion.div>
            ) : (
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                  {getContent('services', 'process', 'title', 'Our')} <span className="gradient-text">{getContent('services', 'process', 'title_highlight', 'Process')}</span>
                </h2>
                <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
                  {getContent('services', 'process', 'subtitle', 'A proven methodology that delivers results')}
                </p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {[
                {
                  step: '01',
                  title: getContent('services', 'process', 'step_1_title', 'Discovery'),
                  description: getContent('services', 'process', 'step_1_description', 'We understand your business goals, requirements, and technical needs through detailed consultation.')
                },
                {
                  step: '02',
                  title: getContent('services', 'process', 'step_2_title', 'Planning'),
                  description: getContent('services', 'process', 'step_2_description', 'We create a comprehensive project plan with timelines, milestones, and resource allocation.')
                },
                {
                  step: '03',
                  title: getContent('services', 'process', 'step_3_title', 'Development'),
                  description: getContent('services', 'process', 'step_3_description', 'Our expert team builds your solution using agile methodology with regular updates and feedback.')
                },
                {
                  step: '04',
                  title: getContent('services', 'process', 'step_4_title', 'Delivery'),
                  description: getContent('services', 'process', 'step_4_description', 'We deploy your solution and provide ongoing support to ensure optimal performance.')
                }
              ] && Array.isArray([
                {
                  step: '01',
                  title: getContent('services', 'process', 'step_1_title', 'Discovery'),
                  description: getContent('services', 'process', 'step_1_description', 'We start by understanding your business needs, goals, and challenges through detailed consultation.')
                },
                {
                  step: '02',
                  title: getContent('services', 'process', 'step_2_title', 'Planning'),
                  description: getContent('services', 'process', 'step_2_description', 'Our team creates a comprehensive project plan with timelines, milestones, and resource allocation.')
                },
                {
                  step: '03',
                  title: getContent('services', 'process', 'step_3_title', 'Development'),
                  description: getContent('services', 'process', 'step_3_description', 'We build your solution using agile methodologies with regular updates and feedback loops.')
                },
                {
                  step: '04',
                  title: getContent('services', 'process', 'step_4_title', 'Delivery'),
                  description: getContent('services', 'process', 'step_4_description', 'We deploy your solution and provide ongoing support to ensure optimal performance.')
                }
              ]) && [
                {
                  step: '01',
                  title: getContent('services', 'process', 'step_1_title', 'Discovery'),
                  description: getContent('services', 'process', 'step_1_description', 'We start by understanding your business needs, goals, and challenges through detailed consultation.')
                },
                {
                  step: '02',
                  title: getContent('services', 'process', 'step_2_title', 'Planning'),
                  description: getContent('services', 'process', 'step_2_description', 'Our team creates a comprehensive project plan with timelines, milestones, and resource allocation.')
                },
                {
                  step: '03',
                  title: getContent('services', 'process', 'step_3_title', 'Development'),
                  description: getContent('services', 'process', 'step_3_description', 'We build your solution using agile methodologies with regular updates and feedback loops.')
                },
                {
                  step: '04',
                  title: getContent('services', 'process', 'step_4_title', 'Delivery'),
                  description: getContent('services', 'process', 'step_4_description', 'We deploy your solution and provide ongoing support to ensure optimal performance.')
                }
              ].map((process, index) => (
                isClient ? (
                  <motion.div
                    key={process.step}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="text-center"
                  >
                    <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                      {process.step}
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">
                      {process.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {process.description}
                    </p>
                  </motion.div>
                ) : (
                  <div key={process.step} className="text-center">
                    <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                      {process.step}
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">
                      {process.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {process.description}
                    </p>
                  </div>
                )
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-24 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="container">
            {isClient ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                  Ready to Get Started?
                </h2>
                <p className="mt-4 text-lg text-blue-100 max-w-3xl mx-auto">
                  Let's discuss your project requirements and create a custom solution
                  that drives your business forward.
                </p>
                <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                    href="/contact"
                    className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors"
                  >
                    Start Your Project
                  </Link>
                  <Link
                    href="/portfolio"
                    className="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors"
                  >
                    View Our Work
                  </Link>
                </div>
              </motion.div>
            ) : (
              <div className="text-center">
                <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                  Ready to Get Started?
                </h2>
                <p className="mt-4 text-lg text-blue-100 max-w-3xl mx-auto">
                  Let's discuss your project requirements and create a custom solution
                  that drives your business forward.
                </p>
                <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                    href="/contact"
                    className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors"
                  >
                    Start Your Project
                  </Link>
                  <Link
                    href="/portfolio"
                    className="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors"
                  >
                    View Our Work
                  </Link>
                </div>
              </div>
            )}
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
