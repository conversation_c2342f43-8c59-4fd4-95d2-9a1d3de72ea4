'use client';

import { motion } from 'framer-motion';
import {
  UserGroupIcon,
  LightBulbIcon,
  ShieldCheckIcon,
  HeartIcon,
  TrophyIcon,
  GlobeAltIcon,
  RocketLaunchIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { useStaticContent } from '@/lib/hooks/use-static-content';

const values = [
  {
    name: 'Innovation',
    description: 'We stay ahead of technology trends and embrace cutting-edge solutions to deliver exceptional results.',
    icon: LightBulbIcon,
  },
  {
    name: 'Quality',
    description: 'We deliver excellence in every project, ensuring robust, scalable, and maintainable solutions.',
    icon: ShieldCheckIcon,
  },
  {
    name: 'Partnership',
    description: 'We work as an extension of your team, fostering collaboration and transparent communication.',
    icon: HeartIcon,
  },
  {
    name: 'Growth',
    description: 'We are committed to continuous learning and helping our clients achieve sustainable growth.',
    icon: RocketLaunchIcon,
  },
];

const stats = [
  { name: 'Years of Experience', value: '10+' },
  { name: 'Projects Completed', value: '500+' },
  { name: 'Happy Clients', value: '200+' },
  { name: 'Team Members', value: '50+' },
  { name: 'Countries Served', value: '25+' },
  { name: 'Technologies Mastered', value: '100+' },
];

const milestones = [
  {
    year: '2014',
    title: 'Company Founded',
    description: 'Started as a small team with a big vision to democratize technology.',
  },
  {
    year: '2016',
    title: 'First Major Client',
    description: 'Secured our first enterprise client and delivered a game-changing solution.',
  },
  {
    year: '2018',
    title: 'Team Expansion',
    description: 'Grew to 25+ team members and opened our second office.',
  },
  {
    year: '2020',
    title: 'Global Reach',
    description: 'Expanded internationally and started serving clients across 5 continents.',
  },
  {
    year: '2022',
    title: 'Innovation Hub',
    description: 'Launched our R&D division focusing on AI and emerging technologies.',
  },
  {
    year: '2024',
    title: 'Industry Leader',
    description: 'Recognized as a leading software development company with 500+ successful projects.',
  },
];

export default function AboutPage() {
  const { getContent } = useStaticContent();

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="relative py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center max-w-4xl mx-auto"
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="mb-8"
              >
                <span className="inline-flex items-center rounded-full bg-blue-50 px-6 py-2 text-sm font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                  <SparklesIcon className="w-4 h-4 mr-2" />
                  Our Story
                </span>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl"
              >
                {getContent('about', 'hero', 'title', 'Building the Future of')}{' '}
                <span className="gradient-text">{getContent('about', 'hero', 'title_highlight', 'Software')}</span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl"
              >
                {getContent('about', 'hero', 'subtitle', 'Founded in 2022, Technoloway has been at the forefront of software innovation, helping businesses transform their ideas into digital reality. We specialize in creating scalable, modern solutions that drive growth and efficiency.')}
              </motion.p>
            </motion.div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-white">
          <div className="container">
            <div className="grid grid-cols-2 gap-8 md:grid-cols-3 lg:grid-cols-6">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="text-3xl font-bold text-blue-600 sm:text-4xl">
                    {stat.value}
                  </div>
                  <div className="mt-2 text-sm text-gray-600">
                    {stat.name}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-24 bg-gray-50">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                  {getContent('about', 'mission', 'title', 'Our')} <span className="gradient-text">{getContent('about', 'mission', 'title_highlight', 'Mission')}</span>
                </h2>
                <p className="mt-6 text-lg text-gray-600 leading-8">
                  {getContent('about', 'mission', 'description_1', 'To democratize cutting-edge technology and make it accessible to businesses of all sizes. We believe that every company deserves world-class software solutions that can compete with industry giants.')}
                </p>
                <p className="mt-4 text-lg text-gray-600 leading-8">
                  {getContent('about', 'mission', 'description_2', 'OOOOOOOur commitment extends beyond just writing code. We partner with our clients to understand their unique challenges and craft solutions that not only meet their current needs but also scale with their future growth.')}
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="aspect-square bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl p-8 flex items-center justify-center">
                  <div className="text-center">
                    <GlobeAltIcon className="w-24 h-24 text-blue-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Global Impact</h3>
                    <p className="text-gray-600">
                      Serving clients across 25+ countries with innovative solutions
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-24 bg-white">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                {getContent('about', 'values', 'title', 'Our')} <span className="gradient-text">{getContent('about', 'values', 'title_highlight', 'Values')}</span>
              </h2>
              <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
                {getContent('about', 'values', 'subtitle', 'The principles that guide everything we do')}
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => (
                <motion.div
                  key={value.name}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center group"
                >
                  <div className="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-2xl mx-auto mb-6 group-hover:bg-blue-200 transition-colors">
                    <value.icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {value.name}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {value.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Timeline Section */}
        <section className="py-24 bg-gray-50">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Our <span className="gradient-text">Journey</span>
              </h2>
              <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
                From a small startup to an industry leader, here are the key milestones that shaped our company.
              </p>
            </motion.div>

            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-gray-300" />

              <div className="space-y-12">
                {milestones.map((milestone, index) => (
                  <motion.div
                    key={milestone.year}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className={`relative flex items-center ${
                      index % 2 === 0 ? 'justify-start' : 'justify-end'
                    }`}
                  >
                    {/* Timeline dot */}
                    <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg" />

                    <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                      <div className="bg-white p-6 rounded-lg shadow-lg border border-gray-200">
                        <div className="text-2xl font-bold text-blue-600 mb-2">
                          {milestone.year}
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {milestone.title}
                        </h3>
                        <p className="text-gray-600">
                          {milestone.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-24 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to Start Your Journey?
              </h2>
              <p className="mt-4 text-lg text-blue-100 max-w-3xl mx-auto">
                Join hundreds of satisfied clients who have transformed their businesses with our solutions.
                Let's build something amazing together.
              </p>
              <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/contact"
                  className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors"
                >
                  Get Started Today
                </a>
                <a
                  href="/portfolio"
                  className="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors"
                >
                  View Our Work
                </a>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
