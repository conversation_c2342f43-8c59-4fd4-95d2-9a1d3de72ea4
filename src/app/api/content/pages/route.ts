import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'
import { promises as fs } from 'fs'
import path from 'path'

interface EditableText {
  id: string
  text: string
  line: number
  description: string
  filePath: string
}

interface PageData {
  id: string
  name: string
  path: string
  icon: string
  sourceCode: string
  editableTexts: EditableText[]
}

interface SaveRequest {
  textId: string
  newText: string
  filePath: string
  line: number
}

// Function to extract text content from source code
function extractTextContent(sourceCode: string, filePath: string): EditableText[] {
  const texts: EditableText[] = []
  const lines = sourceCode.split('\n')
  const seenTexts = new Set<string>()
  const lineKeyCounts: Record<number, number> = {} // For unique keys per line

  // --- Universal getContent() fallback extraction (entire file) ---
  const getContentGlobalRegex = /getContent\(([^)]*)\)/g
  let match
  while ((match = getContentGlobalRegex.exec(sourceCode)) !== null) {
    // Find all string arguments
    const stringArgs = [...match[1].matchAll(/['"`]([^'"`]+)['"`]/g)].map(m => m[1])
    if (stringArgs.length > 0) {
      const text = stringArgs[stringArgs.length - 1]
      if (isUserFacingText(text) && !seenTexts.has(text)) {
        seenTexts.add(text)
        // Find the line number for this match
        const before = sourceCode.slice(0, match.index)
        const line = before.split('\n').length
        // Ensure unique key per line
        lineKeyCounts[line] = (lineKeyCounts[line] || 0) + 1
        texts.push({
          id: `${filePath}-getcontent-global-${line}-${lineKeyCounts[line]}`,
          text,
          line,
          description: `Content fallback: "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`,
          filePath
        })
      }
    }
  }

  // --- Line-by-line extraction for other patterns ---
  lines.forEach((line, lineNumber) => {
    // Skip only obvious non-content lines
    if (line.trim().startsWith('//') || 
        line.trim().startsWith('import') || 
        line.trim().startsWith('export') ||
        line.trim().startsWith('/*') ||
        line.trim().startsWith('*') ||
        line.trim() === '' ||
        line.trim() === '{' ||
        line.trim() === '}' ||
        line.trim() === '};' ||
        line.trim() === ')' ||
        line.trim() === ');') {
      return
    }

    // Look for JSX text content (text between tags) - this is what users see
    const jsxTextMatches = line.match(/>([^<>{}\n]{1,})</g)
    if (jsxTextMatches) {
      jsxTextMatches.forEach((match, index) => {
        const text = match.replace(/[><]/g, '').trim()
        if (isUserFacingText(text) && !seenTexts.has(text)) {
          seenTexts.add(text)
          texts.push({
            id: `${filePath}-jsx-${lineNumber + 1}-${index}`,
            text,
            line: lineNumber + 1,
            description: `Display text: "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`,
            filePath
          })
        }
      })
    }

    // Look for JSX text content in children (more comprehensive)
    const jsxChildrenMatches = line.match(/>\s*([^<>{}\n]{1,})\s*</g)
    if (jsxChildrenMatches) {
      jsxChildrenMatches.forEach((match, index) => {
        const text = match.replace(/[><]/g, '').trim()
        if (isUserFacingText(text) && !seenTexts.has(text)) {
          seenTexts.add(text)
          texts.push({
            id: `${filePath}-jsx-children-${lineNumber + 1}-${index}`,
            text,
            line: lineNumber + 1,
            description: `JSX content: "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`,
            filePath
          })
        }
      })
    }

    // Look for any text content in JSX (very broad pattern)
    const anyJsxTextMatches = line.match(/>([^<>]+)</g)
    if (anyJsxTextMatches) {
      anyJsxTextMatches.forEach((match, index) => {
        const text = match.replace(/[><]/g, '').trim()
        if (isUserFacingText(text) && !seenTexts.has(text)) {
          seenTexts.add(text)
          texts.push({
            id: `${filePath}-any-jsx-${lineNumber + 1}-${index}`,
            text,
            line: lineNumber + 1,
            description: `Any JSX text: "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`,
            filePath
          })
        }
      })
    }

    // Look for static data arrays that contain user-facing content
    const staticDataMatches = line.match(/(name|title|description|value|year|label|text|content|heading|subtitle|caption|message|info|detail|summary):\s*['"`]([^'"`]{1,})['"`]/g)
    if (staticDataMatches) {
      staticDataMatches.forEach((match, index) => {
        const parts = match.match(/(name|title|description|value|year|label|text|content|heading|subtitle|caption|message|info|detail|summary):\s*['"`]([^'"`]+)['"`]/)
        if (parts && parts[2]) {
          const text = parts[2]
          if (isUserFacingText(text) && !seenTexts.has(text)) {
            seenTexts.add(text)
            texts.push({
              id: `${filePath}-static-${lineNumber + 1}-${index}`,
              text,
              line: lineNumber + 1,
              description: `Static ${parts[1]}: "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`,
              filePath
            })
          }
        }
      })
    }

    // Look for any property with string value (very broad pattern)
    const anyPropertyMatches = line.match(/(\w+):\s*['"`]([^'"`]{1,})['"`]/g)
    if (anyPropertyMatches) {
      anyPropertyMatches.forEach((match, index) => {
        const parts = match.match(/(\w+):\s*['"`]([^'"`]+)['"`]/)
        if (parts && parts[2]) {
          const text = parts[2]
          if (isUserFacingText(text) && !seenTexts.has(text)) {
            seenTexts.add(text)
            texts.push({
              id: `${filePath}-any-prop-${lineNumber + 1}-${index}`,
              text,
              line: lineNumber + 1,
              description: `Property ${parts[1]}: "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`,
              filePath
            })
          }
        }
      })
    }

    // Look for array items (like features, pricing info)
    const arrayItemMatches = line.match(/^\s*['"`]([^'"`]{1,})['"`],?\s*$/g)
    if (arrayItemMatches) {
      arrayItemMatches.forEach((match, index) => {
        const parts = match.match(/['"`]([^'"`]+)['"`]/)
        if (parts && parts[1]) {
          const text = parts[1]
          if (isUserFacingText(text) && !seenTexts.has(text)) {
            seenTexts.add(text)
            texts.push({
              id: `${filePath}-array-${lineNumber + 1}-${index}`,
              text,
              line: lineNumber + 1,
              description: `List item: "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`,
              filePath
            })
          }
        }
      })
    }

    // Look for object properties in arrays
    const objectPropertyMatches = line.match(/(\w+):\s*['"`]([^'"`]{1,})['"`],?\s*$/g)
    if (objectPropertyMatches) {
      objectPropertyMatches.forEach((match, index) => {
        const parts = match.match(/(\w+):\s*['"`]([^'"`]+)['"`]/)
        if (parts && parts[2]) {
          const text = parts[2]
          if (isUserFacingText(text) && !seenTexts.has(text)) {
            seenTexts.add(text)
            texts.push({
              id: `${filePath}-object-prop-${lineNumber + 1}-${index}`,
              text,
              line: lineNumber + 1,
              description: `Object ${parts[1]}: "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`,
              filePath
            })
          }
        }
      })
    }

    // Look for any string literal (very broad pattern)
    const anyStringMatches = line.match(/['"`]([^'"`]{1,})['"`]/g)
    if (anyStringMatches) {
      anyStringMatches.forEach((match, index) => {
        const text = match.replace(/['"`]/g, '')
        if (isUserFacingText(text) && !seenTexts.has(text)) {
          seenTexts.add(text)
          texts.push({
            id: `${filePath}-any-string-${lineNumber + 1}-${index}`,
            text,
            line: lineNumber + 1,
            description: `String literal: "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"`,
            filePath
          })
        }
      })
    }
  })

  // Sort texts by priority and line number
  texts.sort((a, b) => {
    const priorityA = getPriority(a)
    const priorityB = getPriority(b)
    
    if (priorityA !== priorityB) {
      return priorityB - priorityA
    }
    
    return a.line - b.line
  })

  return texts
}

// Function to get priority for sorting texts
const getPriority = (text: EditableText) => {
  const description = text.description.toLowerCase()
  const textLower = text.text.toLowerCase()
  const id = text.id.toLowerCase()
  
  // Hero/title content gets highest priority
  if (description.includes('title') || description.includes('hero') || 
      description.includes('heading') || description.includes('main') ||
      textLower.includes('building') || textLower.includes('future') ||
      textLower.includes('welcome') || textLower.includes('transform')) {
    return 1
  }
  
  // Main content sections
  if (description.includes('mission') || description.includes('subtitle') || 
      description.includes('description') && !description.includes('static') ||
      description.includes('content') || description.includes('text')) {
    return 2
  }
  
  // Navigation and UI elements
  if (description.includes('nav') || description.includes('menu') || 
      description.includes('button') || description.includes('link') ||
      description.includes('cta') || description.includes('action')) {
    return 3
  }
  
  // Static data arrays
  if (description.includes('static') || description.includes('values') || 
      description.includes('stats') || description.includes('milestones') ||
      description.includes('features') || description.includes('services') ||
      description.includes('pricing') || description.includes('plans')) {
    return 4
  }
  
  // List items and array content
  if (description.includes('list') || description.includes('array') ||
      description.includes('item') || description.includes('object')) {
    return 5
  }
  
  // Other content
  return 6
}

// Smart function to determine if text is user-facing
function isUserFacingText(text: string): boolean {
  // Must be at least 2 characters
  if (text.length < 2) return false
  
  // Increased limit to capture longer content (was 1000, now 5000)
  if (text.length > 5000) return false
  
  // Must contain letters
  if (!/[a-zA-Z]/.test(text)) return false
  
  // Filter out CSS classes and technical content
  if (text.includes('text-') || 
      text.includes('font-') || 
      text.includes('bg-') || 
      text.includes('border-') || 
      text.includes('p-') || 
      text.includes('m-') || 
      text.includes('w-') || 
      text.includes('h-') || 
      text.includes('flex') || 
      text.includes('grid') || 
      text.includes('rounded') || 
      text.includes('shadow') || 
      text.includes('transition') || 
      text.includes('hover:') || 
      text.includes('focus:') || 
      text.includes('active:') || 
      text.includes('sm:') || 
      text.includes('md:') || 
      text.includes('lg:') || 
      text.includes('xl:') || 
      text.includes('2xl:') ||
      text.includes('className') ||
      text.includes('src=') ||
      text.includes('href=') ||
      text.includes('alt=') ||
      text.includes('id=') ||
      text.includes('data-') ||
      text.includes('aria-') ||
      text.includes('role=') ||
      text.includes('type=') ||
      text.includes('value=') ||
      text.includes('placeholder=') ||
      text.includes('required=') ||
      text.includes('disabled=') ||
      text.includes('readonly=') ||
      text.includes('autofocus=') ||
      text.includes('autocomplete=') ||
      text.includes('maxlength=') ||
      text.includes('minlength=') ||
      text.includes('pattern=') ||
      text.includes('form=') ||
      text.includes('list=') ||
      text.includes('dir=') ||
      text.includes('lang=') ||
      text.includes('spellcheck=') ||
      text.includes('contenteditable=') ||
      text.includes('suppresscontenteditablewarning=') ||
      text.includes('suppresshydrationwarning=') ||
      text.includes('itemscope=') ||
      text.includes('itemtype=') ||
      text.includes('itemprop=') ||
      text.includes('itemref=') ||
      text.includes('itemid=') ||
      text.includes('dangerouslysetinnerhtml=') ||
      text.includes('__html') ||
      text.includes('__dangerouslysetinnerhtml') ||
      text.includes('__self') ||
      text.includes('__source') ||
      text.includes('__proto__') ||
      text.includes('useState') ||
      text.includes('useEffect') ||
      text.includes('onClick') ||
      text.includes('onChange') ||
      text.includes('onSubmit') ||
      text.includes('target=') ||
      text.includes('rel=') ||
      text.includes('type=') ||
      text.includes('value=') ||
      text.includes('defaultValue=') ||
      text.includes('placeholder=') ||
      text.includes('aria-') ||
      text.includes('role=') ||
      text.includes('tabIndex=') ||
      text.includes('disabled=') ||
      text.includes('required=') ||
      text.includes('readOnly=') ||
      text.includes('autoFocus=') ||
      text.includes('autoComplete=') ||
      text.includes('maxLength=') ||
      text.includes('minLength=') ||
      text.includes('pattern=') ||
      text.includes('form=') ||
      text.includes('list=') ||
      text.includes('dir=') ||
      text.includes('lang=') ||
      text.includes('spellCheck=') ||
      text.includes('contentEditable=') ||
      text.includes('suppressContentEditableWarning=') ||
      text.includes('suppressHydrationWarning=') ||
      text.includes('itemScope=') ||
      text.includes('itemType=') ||
      text.includes('itemProp=') ||
      text.includes('itemRef=') ||
      text.includes('itemID=') ||
      text.includes('dangerouslySetInnerHTML=') ||
      text.includes('__html') ||
      text.includes('__dangerouslySetInnerHTML') ||
      text.includes('__self') ||
      text.includes('__source') ||
      text.includes('__proto__') ||
      text.includes('constructor') ||
      text.includes('prototype') ||
      text.includes('toString') ||
      text.includes('valueOf') ||
      text.includes('hasOwnProperty') ||
      text.includes('isPrototypeOf') ||
      text.includes('propertyIsEnumerable') ||
      text.includes('toLocaleString') ||
      text.includes('toLocaleDateString') ||
      text.includes('toLocaleTimeString') ||
      text.includes('toLocaleLowerCase') ||
      text.includes('toLocaleUpperCase') ||
      text.includes('toFixed') ||
      text.includes('toExponential') ||
      text.includes('toPrecision') ||
      text.includes('charAt') ||
      text.includes('charCodeAt') ||
      text.includes('indexOf') ||
      text.includes('lastIndexOf') ||
      text.includes('localeCompare') ||
      text.includes('match') ||
      text.includes('replace') ||
      text.includes('search') ||
      text.includes('slice') ||
      text.includes('split') ||
      text.includes('substring') ||
      text.includes('substr') ||
      text.includes('toLowerCase') ||
      text.includes('toUpperCase') ||
      text.includes('trim') ||
      text.includes('trimLeft') ||
      text.includes('trimRight') ||
      text.includes('concat') ||
      text.includes('join') ||
      text.includes('pop') ||
      text.includes('push') ||
      text.includes('reverse') ||
      text.includes('shift') ||
      text.includes('unshift') ||
      text.includes('splice') ||
      text.includes('sort') ||
      text.includes('filter') ||
      text.includes('map') ||
      text.includes('reduce') ||
      text.includes('reduceRight') ||
      text.includes('every') ||
      text.includes('some') ||
      text.includes('forEach') ||
      text.includes('find') ||
      text.includes('findIndex') ||
      text.includes('includes') ||
      text.includes('keys') ||
      text.includes('values') ||
      text.includes('entries') ||
      text.includes('fromEntries') ||
      text.includes('assign') ||
      text.includes('create') ||
      text.includes('defineProperty') ||
      text.includes('defineProperties') ||
      text.includes('getOwnPropertyDescriptor') ||
      text.includes('getOwnPropertyNames') ||
      text.includes('getOwnPropertySymbols') ||
      text.includes('getPrototypeOf') ||
      text.includes('isExtensible') ||
      text.includes('isFrozen') ||
      text.includes('isSealed') ||
      text.includes('preventExtensions') ||
      text.includes('seal') ||
      text.includes('freeze') ||
      text.includes('setPrototypeOf') ||
      text.includes('delete') ||
      text.includes('instanceof') ||
      text.includes('typeof') ||
      text.includes('void') ||
      text.includes('new') ||
      text.includes('this.') ||
      text.includes('super.') ||
      text.includes('extends') ||
      text.includes('implements') ||
      text.includes('interface') ||
      text.includes('type') ||
      text.includes('enum') ||
      text.includes('namespace') ||
      text.includes('module') ||
      text.includes('require(') ||
      text.includes('module.exports') ||
      text.includes('exports.') ||
      text.includes('__dirname') ||
      text.includes('__filename')) {
    return false
  }
  
  // Additional filters for common non-content patterns
  if (text.trim().startsWith('//') || 
      text.trim().startsWith('/*') || 
      text.trim().startsWith('*') ||
      text.trim().startsWith('import ') ||
      text.trim().startsWith('export ') ||
      text.trim().startsWith('const ') ||
      text.trim().startsWith('let ') ||
      text.trim().startsWith('var ') ||
      text.trim().startsWith('function ') ||
      text.trim().startsWith('class ') ||
      text.trim().startsWith('interface ') ||
      text.trim().startsWith('type ') ||
      text.trim().startsWith('enum ') ||
      text.trim().startsWith('namespace ') ||
      text.trim().startsWith('module ') ||
      text.trim().startsWith('declare ') ||
      text.trim().startsWith('abstract ') ||
      text.trim().startsWith('public ') ||
      text.trim().startsWith('private ') ||
      text.trim().startsWith('protected ') ||
      text.trim().startsWith('static ') ||
      text.trim().startsWith('async ') ||
      text.trim().startsWith('await ') ||
      text.trim().startsWith('return ') ||
      text.trim().startsWith('throw ') ||
      text.trim().startsWith('try ') ||
      text.trim().startsWith('catch ') ||
      text.trim().startsWith('finally ') ||
      text.trim().startsWith('if ') ||
      text.trim().startsWith('else ') ||
      text.trim().startsWith('switch ') ||
      text.trim().startsWith('case ') ||
      text.trim().startsWith('default ') ||
      text.trim().startsWith('for ') ||
      text.trim().startsWith('while ') ||
      text.trim().startsWith('do ') ||
      text.trim().startsWith('break ') ||
      text.trim().startsWith('continue ') ||
      text.trim().startsWith('with ') ||
      text.trim().startsWith('debugger ') ||
      text.trim().startsWith('yield ') ||
      text.trim().startsWith('super ') ||
      text.trim().startsWith('this ') ||
      text.trim().startsWith('new ') ||
      text.trim().startsWith('delete ') ||
      text.trim().startsWith('typeof ') ||
      text.trim().startsWith('instanceof ') ||
      text.trim().startsWith('void ') ||
      text.trim().startsWith('in ') ||
      text.trim().startsWith('of ') ||
      text.trim().startsWith('get ') ||
      text.trim().startsWith('set ')) {
    return false
  }
  
  return true
}

// Function to read and analyze a page file
async function analyzePageFile(filePath: string, pageId: string, pageName: string, pagePath: string): Promise<PageData> {
  try {
    const fullPath = path.join(process.cwd(), filePath)
    const sourceCode = await fs.readFile(fullPath, 'utf-8')
    const editableTexts = extractTextContent(sourceCode, filePath)

    return {
      id: pageId,
      name: pageName,
      path: pagePath,
      icon: getPageIcon(pageId),
      sourceCode,
      editableTexts
    }
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error)
    return {
      id: pageId,
      name: pageName,
      path: pagePath,
      icon: getPageIcon(pageId),
      sourceCode: `// Error reading file: ${filePath}`,
      editableTexts: []
    }
  }
}

// Function to save text changes back to the file
async function saveTextChange(saveRequest: SaveRequest): Promise<boolean> {
  try {
    console.log('saveTextChange called with:', saveRequest)
    
    const fullPath = path.join(process.cwd(), saveRequest.filePath)
    console.log('Full file path:', fullPath)
    
    const sourceCode = await fs.readFile(fullPath, 'utf-8')
    console.log('File read successfully, length:', sourceCode.length)
    
    // We need to find the original text that was extracted
    // Let's re-extract the text to find the exact match
    const editableTexts = extractTextContent(sourceCode, saveRequest.filePath)
    console.log('Extracted texts count:', editableTexts.length)
    console.log('Looking for text ID:', saveRequest.textId)
    
    const textToUpdate = editableTexts.find(text => text.id === saveRequest.textId)
    
    if (!textToUpdate) {
      console.log('Available text IDs:', editableTexts.map(t => t.id).slice(0, 10))
      throw new Error(`Text with ID ${saveRequest.textId} not found in file`)
    }
    
    const originalText = textToUpdate.text
    const newText = saveRequest.newText
    
    // If the text hasn't changed, no need to update
    if (originalText === newText) {
      return false
    }
    
    // Escape special regex characters in the original text
    const escapedOriginalText = originalText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    
    // Parse the text ID to determine the type of change
    const textIdParts = saveRequest.textId.split('-')
    const changeType = textIdParts[1] // 'jsx', 'string', or 'object'
    
    let newSourceCode = sourceCode
    let changed = false
    
    if (changeType === 'jsx') {
      // Replace JSX text content
      const jsxPattern = new RegExp(`>(${escapedOriginalText})<`, 'g')
      newSourceCode = sourceCode.replace(jsxPattern, (match, oldText) => {
        changed = true
        return `>${newText}<`
      })
    } else if (changeType === 'string') {
      // Replace string literal - find the exact string with quotes
      const stringPattern = new RegExp(`(['"\`])(${escapedOriginalText})\\1`, 'g')
      newSourceCode = sourceCode.replace(stringPattern, (match, quote, oldText) => {
        changed = true
        return `${quote}${newText}${quote}`
      })
    } else if (changeType === 'object') {
      // Replace object property value
      const objectPattern = new RegExp(`(\\w+):\\s*(['"\`])(${escapedOriginalText})\\2`, 'g')
      newSourceCode = sourceCode.replace(objectPattern, (match, propName, quote, oldText) => {
        changed = true
        return `${propName}: ${quote}${newText}${quote}`
      })
    } else if (changeType === 'getcontent') {
      // Replace getContent() fallback text
      const getContentPattern = new RegExp(`(getContent\\([^)]*,\\s*['"\`])(${escapedOriginalText})(['"\`])`, 'g')
      newSourceCode = sourceCode.replace(getContentPattern, (match, prefix, oldText, suffix) => {
        changed = true
        return `${prefix}${newText}${suffix}`
      })
    }
    
    // Only update if something actually changed
    if (changed && newSourceCode !== sourceCode) {
      await fs.writeFile(fullPath, newSourceCode, 'utf-8')
      return true
    }
    
    return false
  } catch (error) {
    console.error('Error saving text change:', error)
    throw error
  }
}

function getPageIcon(pageId: string): string {
  switch (pageId) {
    case 'home': return 'HomeIcon'
    case 'about': return 'InformationCircleIcon'
    case 'services': return 'BriefcaseIcon'
    case 'contact': return 'PhoneIcon'
    case 'projects': return 'FolderIcon'
    case 'team': return 'UserGroupIcon'
    case 'blog': return 'DocumentTextIcon'
    case 'portfolio': return 'PhotoIcon'
    case 'technologies': return 'ChipIcon'
    case 'layout': return 'DocumentTextIcon'
    default: return 'GlobeAltIcon'
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Define the pages to analyze
    const pagesToAnalyze = [
      {
        filePath: 'src/app/page.tsx',
        pageId: 'home',
        pageName: 'Home',
        pagePath: '/'
      },
      {
        filePath: 'src/app/about/page.tsx',
        pageId: 'about',
        pageName: 'About',
        pagePath: '/about'
      },
      {
        filePath: 'src/app/services/page.tsx',
        pageId: 'services',
        pageName: 'Services',
        pagePath: '/services'
      },
      {
        filePath: 'src/app/contact/page.tsx',
        pageId: 'contact',
        pageName: 'Contact',
        pagePath: '/contact'
      },
      {
        filePath: 'src/app/projects/page.tsx',
        pageId: 'projects',
        pageName: 'Projects',
        pagePath: '/projects'
      },
      {
        filePath: 'src/app/team/page.tsx',
        pageId: 'team',
        pageName: 'Team',
        pagePath: '/team'
      },
      {
        filePath: 'src/app/blog/page.tsx',
        pageId: 'blog',
        pageName: 'Blog',
        pagePath: '/blog'
      },
      {
        filePath: 'src/app/portfolio/page.tsx',
        pageId: 'portfolio',
        pageName: 'Portfolio',
        pagePath: '/portfolio'
      },
      {
        filePath: 'src/app/technologies/page.tsx',
        pageId: 'technologies',
        pageName: 'Technologies',
        pagePath: '/technologies'
      },
      {
        filePath: 'src/app/layout.tsx',
        pageId: 'layout',
        pageName: 'Layout',
        pagePath: '/layout'
      }
    ]

    // Analyze each page
    const pages: PageData[] = []
    for (const page of pagesToAnalyze) {
      const pageData = await analyzePageFile(page.filePath, page.pageId, page.pageName, page.pagePath)
      pages.push(pageData)
    }

    return NextResponse.json({
      success: true,
      pages,
      totalPages: pages.length,
      totalTexts: pages.reduce((acc, page) => acc + page.editableTexts.length, 0)
    })

  } catch (error) {
    console.error('Error analyzing pages:', error)
    return NextResponse.json({ 
      error: 'Failed to analyze pages',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: SaveRequest = await request.json()
    
    console.log('Received save request:', body)
    
    // Validate request
    if (!body.textId || !body.newText || !body.filePath || !body.line) {
      console.log('Missing required fields:', { textId: !!body.textId, newText: !!body.newText, filePath: !!body.filePath, line: !!body.line })
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Save the text change
    const success = await saveTextChange(body)

    return NextResponse.json({
      success: true,
      message: success ? 'Text updated successfully' : 'No changes made',
      textId: body.textId,
      newText: body.newText
    })

  } catch (error) {
    console.error('Error saving text change:', error)
    return NextResponse.json({ 
      error: 'Failed to save text change',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 