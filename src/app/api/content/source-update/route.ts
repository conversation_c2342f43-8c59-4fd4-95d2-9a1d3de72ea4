import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'
import { SourceLocator } from '@/lib/source-locator'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { oldText, newText, pagePath, targetLine, targetFile } = body

    // Validate required fields
    if (!oldText || !newText || !pagePath) {
      return NextResponse.json(
        { error: 'Missing required fields: oldText, newText, pagePath' },
        { status: 400 }
      )
    }

    console.log('Source update request:', {
      oldText,
      newText,
      pagePath,
      targetLine,
      targetFile,
      timestamp: new Date().toISOString(),
      user: session.user.email
    })

    try {
      let location: any
      
          if (targetFile && targetLine) {
      // Use specific target if provided
      // Ensure the file path has the correct prefix
      const normalizedTargetFile = targetFile.startsWith('src/') ? targetFile : `src/${targetFile}`
      
      console.log(`Looking for text "${oldText}" at line ${targetLine} in file: ${normalizedTargetFile}`)
      
      const locations = await SourceLocator.findAllTextLocations(normalizedTargetFile, oldText)
      console.log(`Found ${locations.length} locations for text "${oldText}":`)
      locations.forEach((loc, index) => {
        console.log(`  ${index + 1}. Line ${loc.lineNumber}: "${loc.fullLine.trim()}"`)
      })
      
      location = locations.find(loc => Number(loc.lineNumber) === Number(targetLine))
      
      if (!location) {
        return NextResponse.json(
          { error: `Text "${oldText}" not found at line ${targetLine} in file ${normalizedTargetFile}` },
          { status: 404 }
        )
      }
    } else {
        // Fallback to searching all files
        location = await SourceLocator.searchTextInFiles(oldText, pagePath)
        
        if (!location) {
          return NextResponse.json(
            { error: `Text "${oldText}" not found in any source files for page ${pagePath}` },
            { status: 404 }
          )
        }
      }

      // Show the user where the text was found
      const locationDescription = SourceLocator.getLocationDescription(location)
      console.log(`Found text at: ${locationDescription}`)

      // Update the text at the found location
      const updateResult = await SourceLocator.updateTextAtLocation({
        location,
        newText
      })

      if (updateResult.success) {
        return NextResponse.json({
          success: true,
          message: updateResult.message,
          data: {
            location: locationDescription,
            oldText,
            newText,
            filePath: location.filePath,
            lineNumber: location.lineNumber,
            columnStart: location.columnStart,
            updatedAt: new Date().toISOString()
          }
        })
      } else {
        return NextResponse.json(
          { error: updateResult.message },
          { status: 400 }
        )
      }

    } catch (error) {
      console.error('Error updating source:', error)
      return NextResponse.json(
        { error: `Failed to update source: ${error instanceof Error ? error.message : 'Unknown error'}` },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error in source update endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET method to find text location without updating
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const searchText = searchParams.get('text')
    const pagePath = searchParams.get('pagePath')

    if (!searchText || !pagePath) {
      return NextResponse.json(
        { error: 'Missing required parameters: text, pagePath' },
        { status: 400 }
      )
    }

    // Clean the pagePath
    const cleanPagePath = pagePath.replace('src/app', '').replace('/page.tsx', '')
    
    // Find all text locations
    const potentialFiles = [
      `src/app${cleanPagePath}/page.tsx`,
      `src/app${cleanPagePath}/layout.tsx`,
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
    ]
    
    let allLocations: Array<{
      filePath: string
      lineNumber: number
      columnStart: number
      columnEnd: number
      fullLine: string
      locationDescription: string
    }> = []
    
    for (const filePath of potentialFiles) {
      try {
        const locations = await SourceLocator.findAllTextLocations(filePath, searchText)
        for (const location of locations) {
          allLocations.push({
            filePath: location.filePath,
            lineNumber: location.lineNumber,
            columnStart: location.columnStart,
            columnEnd: location.columnEnd,
            fullLine: location.fullLine,
            locationDescription: SourceLocator.getLocationDescription(location)
          })
        }
      } catch (error) {
        // File doesn't exist or can't be read, continue to next file
        continue
      }
    }
    
    if (allLocations.length === 0) {
      return NextResponse.json(
        { error: `Text "${searchText}" not found in any source files for page ${pagePath}` },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        text: searchText,
        locations: allLocations,
        totalOccurrences: allLocations.length
      }
    })

  } catch (error) {
    console.error('Error finding text location:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 