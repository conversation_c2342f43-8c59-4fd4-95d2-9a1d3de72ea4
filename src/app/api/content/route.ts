import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'
import { prisma } from '@/lib/prisma'
import { successResponse, errorResponse } from '@/lib/api-utils'
import { getPublicPages, type PublicPage, type PageSection } from '@/lib/page-detector'

// GET /api/content - Fetch all content
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return errorResponse('Unauthorized', 401)
    }

    // Get detected pages structure
    const detectedPages = getPublicPages()
    
    // Fetch existing content from database
    const staticContent = await prisma.staticcontent.findMany({
      where: {
        isactive: true
      },
      orderBy: [
        { page: 'asc' },
        { section: 'asc' },
        { displayorder: 'asc' }
      ]
    })

    // Group content by page and section
    const contentByPage: Record<string, any> = {}
    
    staticContent.forEach(content => {
      if (!contentByPage[content.page]) {
        contentByPage[content.page] = {
          sections: {}
        }
      }
      
      if (!contentByPage[content.page].sections[content.section]) {
        contentByPage[content.page].sections[content.section] = {
          fields: {},
          slides: []
        }
      }
      
      if (content.contenttype === 'hero_slide') {
        try {
          const slideData = JSON.parse(content.content)
          contentByPage[content.page].sections[content.section].slides.push({
            ...slideData,
            id: content.contentkey
          })
        } catch (error) {
          console.error('Error parsing hero slide:', error)
        }
      } else {
        contentByPage[content.page].sections[content.section].fields[content.contentkey] = content.content
      }
    })

    // Merge detected pages with database content
    const mergedPages = detectedPages.map(detectedPage => {
      const dbPage = contentByPage[detectedPage.id]
      
      if (dbPage) {
        return {
          ...detectedPage,
          sections: detectedPage.sections.map(detectedSection => {
            const dbSection = dbPage.sections[detectedSection.id]
            
            if (dbSection) {
              return {
                ...detectedSection,
                fields: {
                  ...detectedSection.fields,
                  ...dbSection.fields
                },
                slides: dbSection.slides.length > 0 ? dbSection.slides : detectedSection.slides
              }
            }
            
            return detectedSection
          })
        }
      }
      
      return detectedPage
    })

    const responseData = {
      pages: mergedPages,
      lastUpdated: new Date().toISOString(),
      totalPages: mergedPages.length,
      totalSections: mergedPages.reduce((acc, page) => acc + page.sections.length, 0)
    }

    return successResponse(responseData)
  } catch (error) {
    console.error('Error fetching content:', error)
    return errorResponse('Failed to fetch content', 500)
  }
}

// PUT /api/content - Update content
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return errorResponse('Unauthorized', 401)
    }

    const body = await request.json()
    const { content, updates, slideUpdates } = body

    // Validate input
    if (!content || !content.pages) {
      return errorResponse('Invalid content structure', 400)
    }

    const transactionData: any[] = []

    // Process field updates
    if (updates && Array.isArray(updates)) {
      for (const update of updates) {
        const { pageId, sectionId, fieldKey, value } = update
        
        // Check if content exists
        const existingContent = await prisma.staticcontent.findFirst({
          where: {
            page: pageId,
            section: sectionId,
            contentkey: fieldKey,
            isactive: true
          }
        })

        if (existingContent) {
          // Update existing content
          transactionData.push(
            prisma.staticcontent.update({
              where: { id: existingContent.id },
              data: {
                content: value,
                updatedat: new Date()
              }
            })
          )
        } else {
          // Create new content
          transactionData.push(
            prisma.staticcontent.create({
              data: {
                page: pageId,
                section: sectionId,
                contentkey: fieldKey,
                contenttype: 'text',
                content: value,
                displayorder: 1,
                isactive: true,
                createdat: new Date(),
                updatedat: new Date()
              }
            })
          )
        }
      }
    }

    // Process slide updates
    if (slideUpdates && Array.isArray(slideUpdates)) {
      for (const update of slideUpdates) {
        const { pageId, sectionId, slideId, slide } = update
        
        // Delete existing slide
        await prisma.staticcontent.deleteMany({
          where: {
            page: pageId,
            section: sectionId,
            contentkey: slideId,
            contenttype: 'hero_slide'
          }
        })

        // Create new slide
        transactionData.push(
          prisma.staticcontent.create({
            data: {
              page: pageId,
              section: sectionId,
              contentkey: slideId,
              contenttype: 'hero_slide',
              content: JSON.stringify(slide),
              displayorder: slide.displayOrder || 1,
              isactive: slide.isActive !== false,
              createdat: new Date(),
              updatedat: new Date()
            }
          })
        )
      }
    }

    // Process full content structure updates
    if (content.pages) {
      for (const page of content.pages) {
        for (const section of page.sections) {
          // Update section fields
          for (const [fieldKey, fieldValue] of Object.entries(section.fields)) {
            const existingContent = await prisma.staticcontent.findFirst({
              where: {
                page: page.id,
                section: section.id,
                contentkey: fieldKey,
                isactive: true
              }
            })

            if (existingContent) {
              transactionData.push(
                prisma.staticcontent.update({
                  where: { id: existingContent.id },
                  data: {
                    content: fieldValue as string,
                    updatedat: new Date()
                  }
                })
              )
            } else {
              transactionData.push(
                prisma.staticcontent.create({
                  data: {
                    page: page.id,
                    section: section.id,
                    contentkey: fieldKey,
                    contenttype: 'text',
                    content: fieldValue as string,
                    displayorder: section.displayOrder || 1,
                    isactive: section.isActive !== false,
                    createdat: new Date(),
                    updatedat: new Date()
                  }
                })
              )
            }
          }

          // Update hero slides
          if (section.slides && Array.isArray(section.slides)) {
            // Delete existing slides for this section
            await prisma.staticcontent.deleteMany({
              where: {
                page: page.id,
                section: section.id,
                contenttype: 'hero_slide'
              }
            })

            // Create new slides
            for (const slide of section.slides) {
              transactionData.push(
                prisma.staticcontent.create({
                  data: {
                    page: page.id,
                    section: section.id,
                    contentkey: slide.id,
                    contenttype: 'hero_slide',
                    content: JSON.stringify(slide),
                    displayorder: slide.displayOrder || 1,
                    isactive: slide.isActive !== false,
                    createdat: new Date(),
                    updatedat: new Date()
                  }
                })
              )
            }
          }
        }
      }
    }

    // Execute all database operations in a transaction
    if (transactionData.length > 0) {
      await prisma.$transaction(transactionData)
    }

    // Log the update
    await prisma.auditlogs.create({
      data: {
        action: 'CONTENT_UPDATE',
        resource: 'STATIC_CONTENT',
        resourceid: 'content_editor',
        userid: BigInt(session.user.id),
        details: JSON.stringify({
          pagesUpdated: content.pages?.length || 0,
          fieldsUpdated: updates?.length || 0,
          slidesUpdated: slideUpdates?.length || 0
        }),
        ipaddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        useragent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return successResponse({
      message: 'Content updated successfully',
      pagesUpdated: content.pages?.length || 0,
      fieldsUpdated: updates?.length || 0,
      slidesUpdated: slideUpdates?.length || 0,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error updating content:', error)
    return errorResponse('Failed to update content', 500)
  }
}

// POST /api/content - Create new content (for specific sections)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return errorResponse('Unauthorized', 401)
    }

    const body = await request.json()
    const { pageId, sectionId, contentKey, contentType, content, displayOrder } = body

    // Validate input
    if (!pageId || !sectionId || !contentKey || !content) {
      return errorResponse('Missing required fields', 400)
    }

    // Check if content already exists
    const existingContent = await prisma.staticcontent.findFirst({
      where: {
        page: pageId,
        section: sectionId,
        contentkey: contentKey,
        isactive: true
      }
    })

    if (existingContent) {
      return errorResponse('Content already exists', 409)
    }

    // Create new content
    const newContent = await prisma.staticcontent.create({
      data: {
        page: pageId,
        section: sectionId,
        contentkey: contentKey,
        contenttype: contentType || 'text',
        content: typeof content === 'object' ? JSON.stringify(content) : content,
        displayorder: displayOrder || 1,
        isactive: true,
        createdat: new Date(),
        updatedat: new Date()
      }
    })

    // Log the creation
    await prisma.auditlogs.create({
      data: {
        action: 'CONTENT_CREATE',
        resource: 'STATIC_CONTENT',
        resourceid: newContent.id.toString(),
        userid: BigInt(session.user.id),
        details: JSON.stringify({
          pageId,
          sectionId,
          contentKey,
          contentType
        }),
        ipaddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        useragent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return successResponse({
      message: 'Content created successfully',
      content: newContent
    })
  } catch (error) {
    console.error('Error creating content:', error)
    return errorResponse('Failed to create content', 500)
  }
}

// DELETE /api/content - Delete content
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return errorResponse('Unauthorized', 401)
    }

    const { searchParams } = new URL(request.url)
    const pageId = searchParams.get('pageId')
    const sectionId = searchParams.get('sectionId')
    const contentKey = searchParams.get('contentKey')

    if (!pageId || !sectionId || !contentKey) {
      return errorResponse('Missing required parameters', 400)
    }

    // Find and delete content
    const contentToDelete = await prisma.staticcontent.findFirst({
      where: {
        page: pageId,
        section: sectionId,
        contentkey: contentKey,
        isactive: true
      }
    })

    if (!contentToDelete) {
      return errorResponse('Content not found', 404)
    }

    await prisma.staticcontent.delete({
      where: { id: contentToDelete.id }
    })

    // Log the deletion
    await prisma.auditlogs.create({
      data: {
        action: 'CONTENT_DELETE',
        resource: 'STATIC_CONTENT',
        resourceid: contentToDelete.id.toString(),
        userid: BigInt(session.user.id),
        details: JSON.stringify({
          pageId,
          sectionId,
          contentKey
        }),
        ipaddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        useragent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return successResponse({
      message: 'Content deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting content:', error)
    return errorResponse('Failed to delete content', 500)
  }
}
