import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'
import { ContentManager } from '../content-manager'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const pagePath = searchParams.get('pagePath')

    if (!pagePath) {
      return NextResponse.json(
        { error: 'Missing required parameter: pagePath' },
        { status: 400 }
      )
    }

    // Get the editable content for the page
    const content = await ContentManager.getPageContent(pagePath)
    const contentMap = await ContentManager.getContentForIframe(pagePath)

    return NextResponse.json({
      success: true,
      data: {
        content,
        contentMap,
        pagePath
      }
    })

  } catch (error) {
    console.error('Error getting page content:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 