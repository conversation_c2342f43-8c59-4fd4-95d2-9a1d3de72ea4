import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'
import fs from 'fs'
import path from 'path'

// POST /api/content/tsx-edit - Edit TSX file content (updated)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      filePath,
      oldText,
      newText,
      startLine,
      endLine,
      startColumn,
      endColumn
    } = body

    console.log('TSX Edit API received:', {
      filePath,
      oldText: oldText?.substring(0, 50) + (oldText?.length > 50 ? '...' : ''),
      newText: newText?.substring(0, 50) + (newText?.length > 50 ? '...' : '')
    })

    // Validate required fields
    if (!filePath || !oldText || newText === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: filePath, oldText, newText' },
        { status: 400 }
      )
    }

    const projectRoot = process.cwd()

    // Handle file path correctly - it may already include 'src/' prefix
    const cleanFilePath = filePath.replace(/\/+/g, '/') // Remove double slashes
    const fullPath = cleanFilePath.startsWith('src/')
      ? path.join(projectRoot, cleanFilePath)
      : path.join(projectRoot, 'src', cleanFilePath)

    // Security check: ensure the file is within the src directory
    const normalizedPath = path.normalize(fullPath)
    const srcDir = path.join(projectRoot, 'src')

    console.log('TSX Edit path check:', {
      originalFilePath: filePath,
      cleanFilePath,
      fullPath,
      normalizedPath,
      exists: fs.existsSync(normalizedPath),
      isTsx: filePath.endsWith('.tsx')
    })

    if (!normalizedPath.startsWith(srcDir)) {
      return NextResponse.json(
        { error: 'Invalid file path' },
        { status: 400 }
      )
    }

    // Check if file exists and is a .tsx file
    if (!fs.existsSync(normalizedPath) || !filePath.endsWith('.tsx')) {
      return NextResponse.json(
        { error: 'File not found or not a TSX file' },
        { status: 404 }
      )
    }

    // Read current file content
    const currentContent = await fs.promises.readFile(normalizedPath, 'utf-8')
    
    // Find and replace the text
    const updatedContent = currentContent.replace(oldText, newText)
    
    // Verify that the replacement was made
    if (updatedContent === currentContent && oldText !== newText) {
      return NextResponse.json(
        { error: 'Text not found in file or no changes made' },
        { status: 400 }
      )
    }

    // Create backup
    const backupPath = `${normalizedPath}.backup.${Date.now()}`
    await fs.promises.writeFile(backupPath, currentContent, 'utf-8')

    // Write updated content
    await fs.promises.writeFile(normalizedPath, updatedContent, 'utf-8')

    console.log('TSX file updated successfully:', {
      filePath,
      oldText: oldText.substring(0, 100) + (oldText.length > 100 ? '...' : ''),
      newText: newText.substring(0, 100) + (newText.length > 100 ? '...' : ''),
      startLine,
      endLine,
      user: session.user.email,
      timestamp: new Date().toISOString()
    })

    return NextResponse.json({
      success: true,
      message: 'File updated successfully',
      filePath,
      backupPath: path.basename(backupPath)
    })
  } catch (error) {
    console.error('Error updating TSX file:', error)
    return NextResponse.json(
      { error: 'Failed to update TSX file' },
      { status: 500 }
    )
  }
}
