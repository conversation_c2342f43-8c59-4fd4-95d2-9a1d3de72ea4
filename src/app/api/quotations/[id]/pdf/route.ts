import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    const quotationId = parseInt(id)

    if (isNaN(quotationId)) {
      return NextResponse.json(
        { success: false, message: 'Invalid quotation ID' },
        { status: 400 }
      )
    }

    // Get quotation details
    const quotation = await prisma.quotationrequests.findUnique({
      where: { id: quotationId },
      include: {
        client: true
      }
    })

    if (!quotation) {
      return NextResponse.json(
        { success: false, message: 'Quotation not found' },
        { status: 404 }
      )
    }

    // Generate quotation PDF
    const pdfContent = generateQuotationPDF(quotation)
    
    return new NextResponse(pdfContent, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="quotation-${quotationId}.pdf"`
      }
    })

  } catch (error) {
    console.error('Error generating quotation PDF:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to generate PDF' },
      { status: 500 }
    )
  }
}

function generateQuotationPDF(quotation: any): Buffer {
  // This is a placeholder implementation
  // In a real application, you would use a PDF generation library
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 300
>>
stream
BT
/F1 12 Tf
50 750 Td
(Quotation Request #${quotation.id}) Tj
0 -20 Td
(Service Type: ${quotation.servicetype}) Tj
0 -20 Td
(Request Date: ${quotation.requestdate}) Tj
0 -20 Td
(Status: ${quotation.status}) Tj
0 -20 Td
(Budget: $${quotation.budget || 'N/A'}) Tj
0 -20 Td
(Timeline: ${quotation.timeline || 'N/A'}) Tj
0 -20 Td
(Client: ${quotation.client?.companyname || quotation.client?.contactname}) Tj
0 -40 Td
(Description:) Tj
0 -20 Td
(${quotation.description || 'No description provided'}) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000626 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
723
%%EOF`

  return Buffer.from(pdfContent)
}
