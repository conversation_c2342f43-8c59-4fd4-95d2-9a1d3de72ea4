import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'
import { prisma } from '@/lib/prisma'

// GET /api/client/dashboard - Get client dashboard data for authenticated user
export const GET = async (request: NextRequest) => {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return Response.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      )
    }

    if (session.user.role !== 'CLIENT') {
      return Response.json(
        { success: false, message: 'Access denied. Client role required.' },
        { status: 403 }
      )
    }

    // Find the client record linked to this user
    const client = await prisma.clients.findFirst({
      where: {
        userid: BigInt(session.user.id)
      },
      include: {
        projects: {
          select: {
            id: true,
            name: true,
            description: true,
            status: true,
            projstartdate: true,
            projcompletiondate: true,
            estimatecost: true,
            estimatetime: true,
            createdat: true,
            updatedat: true,
            messages: {
              select: {
                id: true,
                content: true,
                sendername: true,
                senderrole: true,
                isread: true,
                createdat: true,
                updatedat: true
              },
              orderBy: {
                createdat: 'desc'
              }
            }
          },
          orderBy: {
            createdat: 'desc'
          }
        },
        invoices: {
          select: {
            id: true,
            duedate: true,
            totalamount: true,
            status: true,
            description: true,
            createdat: true,
            updatedat: true,
            payments: {
              select: {
                id: true,
                amount: true,
                paymentdate: true,
                paymentmethod: true,
                status: true,
                createdat: true,
                updatedat: true
              }
            }
          },
          orderBy: {
            createdat: 'desc'
          }
        },
        quotationrequests: {
          select: {
            id: true,
            servicename: true,
            description: true,
            status: true,
            requestdate: true,
            budget: true,
            timeline: true,
            urgency: true,
            createdat: true,
            updatedat: true
          },
          orderBy: {
            createdat: 'desc'
          }
        },
        _count: {
          select: {
            projects: true,
            invoices: true,
            quotationrequests: true
          }
        }
      }
    })

    if (!client) {
      return Response.json(
        {
          success: false,
          message: 'No client account linked to your user account. Please contact support.'
        },
        { status: 404 }
      )
    }

    // Convert BigInt fields to numbers for JSON serialization
    const serializedClient = {
      id: Number(client.id),
      companyname: client.companyname,
      contactname: client.contactname,
      contactemail: client.contactemail,
      contactphone: client.contactphone,
      website: client.website,
      address: client.address,
      city: client.city,
      state: client.state,
      country: client.country,
      zipcode: client.zipcode,
      logourl: client.logourl,
      isactive: client.isactive,
      notes: client.notes,
      createdat: client.createdat,
      updatedat: client.updatedat,
      projects: client.projects.map(project => ({
        ...project,
        id: Number(project.id),
        estimatecost: project.estimatecost ? Number(project.estimatecost) : null,
        projstartdate: project.projstartdate?.toISOString(),
        projcompletiondate: project.projcompletiondate?.toISOString(),
        createdat: project.createdat?.toISOString(),
        updatedat: project.updatedat?.toISOString(),
        messages: project.messages.map(message => ({
          ...message,
          id: Number(message.id),
          createdat: message.createdat?.toISOString(),
          updatedat: message.updatedat?.toISOString()
        }))
      })),
      invoices: client.invoices.map(invoice => ({
        ...invoice,
        id: Number(invoice.id),
        totalamount: invoice.totalamount ? Number(invoice.totalamount) : null,
        duedate: invoice.duedate?.toISOString(),
        createdat: invoice.createdat?.toISOString(),
        updatedat: invoice.updatedat?.toISOString(),
        payments: invoice.payments.map(payment => ({
          ...payment,
          id: Number(payment.id),
          amount: payment.amount ? Number(payment.amount) : null,
          paymentdate: payment.paymentdate?.toISOString(),
          createdat: payment.createdat?.toISOString(),
          updatedat: payment.updatedat?.toISOString()
        }))
      })),
      quotationrequests: client.quotationrequests.map(quotation => ({
        ...quotation,
        id: Number(quotation.id),
        budget: quotation.budget ? Number(quotation.budget) : null,
        requestdate: quotation.requestdate?.toISOString(),
        createdat: quotation.createdat?.toISOString(),
        updatedat: quotation.updatedat?.toISOString()
      })),
      _count: client._count
    }

    return Response.json({
      success: true,
      data: serializedClient
    })
  } catch (error) {
    console.error('Client dashboard API error:', error)
    return Response.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
